#!/usr/bin/env bash

# Runs at the end of git flow release start
#
# Positional arguments:
# $1 The version (including the version prefix)
# $2 The origin remote
# $3 The full branch name (including the release prefix)
# $4 The base from which this release is started
#
# The following variables are available as they are exported by git-flow:
#
# MASTER_BRANCH - The branch defined as Master
# DEVELOP_BRANCH - The branch defined as Develop

VERSION=$1
ORIGIN=$2
BRANCH=$3
BASE=$4

# Implement your script here.

SCRIPT_PATH="$0"; while [ -h "$SCRIPT_PATH" ]; do SCRIPT_PATH=$(readlink "$SCRIPT_PATH"); done
. "$(dirname $SCRIPT_PATH)/modules/functions.sh"

. "$HOOKS_DIR/modules/write-version.sh"
if [ $? -ne 0 ]; then
    exit 1
fi

exit 0
