name: Core

on:
  push:
    branches-ignore:
      - master
      - MOODLE_[0-9]+_<PERSON><PERSON>LE
    tags-ignore:
      - v[0-9]+.[0-9]+.[0-9]+*

env:
  php: 8.1

jobs:
  Grunt:
    runs-on: ubuntu-22.04

    steps:
      - name: Checking out code
        uses: actions/checkout@v3

      - name: Configuring node & npm
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Installing node stuff
        run: npm install

      - name: Running grunt
        run: npx grunt

      - name: Looking for uncommitted changes
        # Add all files to the git index and then run diff --cached to see all changes.
        # This ensures that we get the status of all files, including new files.
        # We ignore npm-shrinkwrap.json to make the tasks immune to npm changes.
        run: |
          git add .
          git reset -- npm-shrinkwrap.json
          git diff --cached --exit-code

  PHPUnit:
    runs-on: ${{ matrix.os }}
    services:
      exttests:
        image: moodlehq/moodle-exttests
        ports:
          - 8080:80
      redis:
        image: redis
        ports:
          - 6379:6379
    strategy:
      fail-fast: false
      matrix:
        include:
          # MySQL builds always run with the lowest PHP supported version.
          - os: ubuntu-22.04
            php: 8.0
            extensions:
            db: mysqli
          # PostgreSQL builds always run with the highest PHP supported version.
          - os: ubuntu-22.04
            php: 8.1
            db: pgsql

    steps:
      - name: Setting up DB mysql
        if: ${{ matrix.db == 'mysqli' }}
        uses: moodlehq/mysql-action@v1
        with:
          collation server: utf8mb4_bin
          mysql version: 8.0
          mysql database: test
          mysql user: test
          mysql password: test
          use tmpfs: true
          tmpfs size: '1024M'
          extra conf: --skip-log-bin

      - name: Setting up DB pgsql
        if: ${{ matrix.db == 'pgsql' }}
        uses: m4nu56/postgresql-action@v1
        with:
          postgresql version: 13
          postgresql db: test
          postgresql user: test
          postgresql password: test

      - name: Configuring git vars
        uses: rlespinasse/github-slug-action@v4

      - name: Setting up PHP ${{ matrix.php }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: ${{ matrix.extensions }}
          ini-values: max_input_vars=5000
          coverage: none

      - name: Checking out code from ${{ env.GITHUB_REF_SLUG }}
        uses: actions/checkout@v3

      - name: Setting up PHPUnit
        env:
          dbtype: ${{ matrix.db }}
        run: |
          echo "pathtophp=$(which php)" >> $GITHUB_ENV # Inject installed pathtophp to env. The template config needs it.
          cp .github/workflows/config-template.php config.php
          mkdir ../moodledata
          sudo locale-gen en_AU.UTF-8
          php admin/tool/phpunit/cli/init.php --no-composer-self-update

      - name: Running PHPUnit tests
        env:
          dbtype: ${{ matrix.db }}
          phpunit_options: ${{ secrets.phpunit_options }}
        run: vendor/bin/phpunit $phpunit_options
