<?php
/**
 * Safe installation script for customcert that checks for existing tables
 * This script prevents pipeline failures when tables already exist
 */

define('CLI_SCRIPT', true);
require_once(__DIR__ . '/../../../config.php');
require_once($CFG->libdir . '/clilib.php');
require_once($CFG->libdir . '/adminlib.php');

global $DB;
$dbman = $DB->get_manager();

// Check if customcert tables already exist
$tables_to_check = [
    'customcert',
    'customcert_templates', 
    'customcert_issues',
    'customcert_pages',
    'customcert_elements'
];

$existing_tables = [];
foreach ($tables_to_check as $table) {
    if ($dbman->table_exists($table)) {
        $existing_tables[] = $table;
    }
}

cli_writeln("Verificando instalação do CustomCert...");

if (count($existing_tables) === count($tables_to_check)) {
    cli_writeln("Todas as tabelas do CustomCert já existem no banco:");
    foreach ($existing_tables as $table) {
        cli_writeln("  ✓ $table");
    }
    
    // Verificar se o plugin está registrado no sistema
    $plugin_version = $DB->get_field('config_plugins', 'value', 
        ['plugin' => 'mod_customcert', 'name' => 'version']);
    
    if (!$plugin_version) {
        cli_writeln("Registrando plugin no sistema...");
        
        // Registrar o plugin como instalado
        $versionrecord = new stdClass();
        $versionrecord->plugin = 'mod_customcert';
        $versionrecord->name = 'version';
        $versionrecord->value = 2023042419;
        $DB->insert_record('config_plugins', $versionrecord);
        
        cli_writeln("Plugin CustomCert registrado com sucesso!");
    } else {
        cli_writeln("Plugin já está registrado com versão: $plugin_version");
    }
    
    // Verificar se o plugin aparece na lista de módulos
    $module_exists = $DB->record_exists('modules', ['name' => 'customcert']);
    if (!$module_exists) {
        cli_writeln("Registrando módulo customcert...");
        
        $module = new stdClass();
        $module->name = 'customcert';
        $module->version = 2023042419;
        $module->cron = 0;
        $module->lastcron = 0;
        $module->search = '';
        $module->visible = 1;
        
        $DB->insert_record('modules', $module);
        cli_writeln("Módulo customcert registrado!");
    } else {
        cli_writeln("Módulo customcert já está registrado.");
    }
    
    cli_writeln("CustomCert está pronto para uso!");
    
} else {
    cli_writeln("Algumas tabelas estão faltando. Execute o upgrade normal do Moodle.");
    cli_writeln("Tabelas existentes: " . implode(', ', $existing_tables));
    cli_writeln("Total de tabelas necessárias: " . count($tables_to_check));
}

exit(0);
