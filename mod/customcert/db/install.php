<?php
// This file is part of the customcert module for Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON><PERSON><PERSON>.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Code to be executed after the plugin's database scheme has been installed is defined here.
 *
 * @package     mod_customcert
 * @category    upgrade
 * @copyright   2013 Mark <PERSON> <<EMAIL>>
 * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Custom code to be run on installing the plugin.
 * This function prevents the installation from failing when tables already exist.
 */
function xmldb_customcert_install() {
    global $DB;

    // Get the database manager
    $dbman = $DB->get_manager();

    // List of tables that should be created by this plugin
    $tables_to_check = [
        'customcert',
        'customcert_templates',
        'customcert_issues',
        'customcert_pages',
        'customcert_elements'
    ];

    $existing_tables = [];

    // Check which tables already exist
    foreach ($tables_to_check as $table) {
        if ($dbman->table_exists($table)) {
            $existing_tables[] = $table;
        }
    }

    // If all tables already exist, mark as installed and skip table creation
    if (count($existing_tables) === count($tables_to_check)) {
        // All tables exist, mark the plugin as installed

        // Check if plugin record already exists
        if (!$DB->record_exists('config_plugins', ['plugin' => 'mod_customcert', 'name' => 'version'])) {
            // Insert version record
            $versionrecord = new stdClass();
            $versionrecord->plugin = 'mod_customcert';
            $versionrecord->name = 'version';
            $versionrecord->value = 2023042419;
            $DB->insert_record('config_plugins', $versionrecord);
        }

        // Check if module is registered
        if (!$DB->record_exists('modules', ['name' => 'customcert'])) {
            $module = new stdClass();
            $module->name = 'customcert';
            $module->version = 2023042419;
            $module->cron = 0;
            $module->lastcron = 0;
            $module->search = '';
            $module->visible = 1;
            $DB->insert_record('modules', $module);
        }

        // CRITICAL: Prevent the install.xml from being processed
        // We do this by temporarily renaming the install.xml file
        global $CFG;
        $install_xml_path = $CFG->dirroot . '/mod/customcert/db/install.xml';
        $backup_xml_path = $CFG->dirroot . '/mod/customcert/db/install.xml.backup';

        if (file_exists($install_xml_path) && !file_exists($backup_xml_path)) {
            rename($install_xml_path, $backup_xml_path);

            // Create a minimal install.xml with a dummy table that we'll check for
            $minimal_xml = '<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="mod/customcert/db" VERSION="20240313" COMMENT="XMLDB file for Moodle mod/customcert - TABLES ALREADY EXIST"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd">
  <TABLES>
    <TABLE NAME="customcert_dummy" COMMENT="Dummy table to satisfy XMLDB requirements - tables already exist">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="dummy" TYPE="char" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>';

            file_put_contents($install_xml_path, $minimal_xml);
        }

        return true;
    }

    // If we reach here, some or all tables are missing
    // Let the normal installation process continue
    return true;
}

/**
 * Post installation cleanup - remove dummy table if it was created
 */
function xmldb_customcert_install_recovery() {
    global $DB;

    $dbman = $DB->get_manager();

    // Check if dummy table exists and remove it
    if ($dbman->table_exists('customcert_dummy')) {
        $table = new xmldb_table('customcert_dummy');
        $dbman->drop_table($table);
    }

    return true;
}
