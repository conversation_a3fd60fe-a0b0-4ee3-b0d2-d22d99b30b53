<?php
use core_course\customfield\mod_handler;
use core_customfield\field_controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot."/config.php");

function format_trails_create_course_fields(){
    $categoryname = 'Informações extras';

    $fields = format_trails_get_fields();

    foreach($fields as $key => $field){

        format_trails_create_course_customfield($field,$categoryname);
    }

    return true;
}

function format_trails_get_fields(){
    $fields = [];

    $configdata = new \stdClass;
    $configdata->required = '0';
    $configdata->uniquevalues = '0';
    $configdata->defaultunit = '3600';
    $configdata->locked = '0';
    $configdata->visibility = '2';
    $configdata->defaultvalue = 0;

    $fieldparam = new \stdClass;
    $fieldparam->name = 'Carga horária';
    $fieldparam->shortname = 'workload_activity';
    $fieldparam->type = 'duration';
    $fieldparam->description = 'Carga horária da atividade';
    $fieldparam->descriptionformat = 1;
    $fieldparam->configdata = json_encode($configdata);

    $fields[] = $fieldparam;

    return $fields;
}

function format_trails_create_course_customfield(stdClass $fieldparam,string $categoryname = '')
{
    $mod_handler = local_modcustomfields\customfield\mod_handler::create();
    
    if(!isset($mod_handler->get_available_field_types()[$fieldparam->type])
    && !$mod_handler->get_available_field_types()[$fieldparam->type])
    {
        mtrace(get_string('customfieldtypenotfound','local_courseblockapi'));
        return ;
    }

    $category = format_trails_get_customfield_category($mod_handler,$categoryname);

    if(!$category)
    {
        $categoryid = $mod_handler->create_category($categoryname);
        $category = $mod_handler->get_categories_with_fields()[$categoryid];
    }

    $field = format_trails_get_customfield_field($category,$fieldparam->shortname);

    if($field)
    {
        mtrace(get_string('customfieldalreadyset','local_courseblockapi'));
        return ;
    }

    $field = field_controller::create(0,(object) ['type'=>$fieldparam->type],$category);
    
    $field_handler = $field->get_handler();
    $field_handler->save_field_configuration($field, $fieldparam);

    mtrace(get_string('customfieldcreated','local_courseblockapi'));
    return true;
}

function format_trails_get_customfield_category($mod_handler, string $categoryname)
{
    $categories = $mod_handler->get_categories_with_fields();

    $category = array_usearch($categories,function($cat) use ($categoryname)
    {
        return $cat->get_formatted_name() === $categoryname;
    });
    
    if(!$category)
    {
        return null;
    }

    return $category;
}

function format_trails_get_customfield_field(core_customfield\category_controller $category_controller, string $fieldshortname)
{

    $field = array_usearch($category_controller->get_fields(),function($field) use ($fieldshortname)
    {
        return $field->get('shortname') === $fieldshortname;
    });
    if(!$field)
    {
        return null;
    }

    return $field;
}
if(!function_exists('array_usearch')) {

    function array_usearch(array $array, Closure $test) {
        $found = false;
        $iterator = new ArrayIterator($array);
        
        while ($found === false && $iterator->valid()) {
            if ($test($iterator->current())) {
                $found = $iterator->key();
            }
            $iterator->next();
        }
        if(!$found) {
            return '';
        }
        return $array[$found];
    }
}