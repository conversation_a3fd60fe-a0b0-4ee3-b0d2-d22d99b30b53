{"version": 3, "file": "block_management_table.min.js", "sources": ["../src/block_management_table.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport PluginManagementTable from './plugin_management_table';\nimport {refreshTableContent} from 'core_table/dynamic';\nimport {call as fetchMany} from 'core/ajax';\nimport Pending from 'core/pending';\nimport {fetchNotifications} from 'core/notification';\n\nexport default class extends PluginManagementTable {\n    constructor() {\n        super();\n        this.addClickHandler(this.handleBlockProtectToggle);\n    }\n\n    /**\n     * Set the block protection state.\n     *\n     * @param {string} plugin\n     * @param {number} state\n     * @returns {Promise}\n     */\n    setBlockProtectState(plugin, state) {\n        return fetchMany([{\n            methodname: 'core_admin_set_block_protection',\n            args: {\n                plugin,\n                state,\n            },\n        }])[0];\n    }\n\n    /**\n     * Handle toggling of block protection.\n     *\n     * @param {HTMLElement} tableRoot\n     * @param {Event} e\n     */\n    async handleBlockProtectToggle(tableRoot, e) {\n        const stateToggle = e.target.closest('[data-action=\"toggleprotectstate\"]');\n        if (stateToggle) {\n            e.preventDefault();\n            const pendingPromise = new Pending('core_table/dynamic:processAction');\n\n            await this.setBlockProtectState(\n                stateToggle.dataset.plugin,\n                stateToggle.dataset.targetState === '1' ? 1 : 0\n            );\n\n            const [updatedRoot] = await Promise.all([\n                refreshTableContent(tableRoot),\n                fetchNotifications(),\n            ]);\n\n            // Refocus on the link that as pressed in the first place.\n            updatedRoot.querySelector(`[data-action=\"toggleprotectstate\"][data-plugin=\"${stateToggle.dataset.plugin}\"]`).focus();\n            pendingPromise.resolve();\n        }\n    }\n}\n"], "names": ["PluginManagementTable", "constructor", "addClickHandler", "this", "handleBlockProtectToggle", "setBlockProtectState", "plugin", "state", "methodname", "args", "tableRoot", "e", "stateToggle", "target", "closest", "preventDefault", "pendingPromise", "Pending", "dataset", "targetState", "updatedRoot", "Promise", "all", "querySelector", "focus", "resolve"], "mappings": "uhBAqB6BA,iCACzBC,2BAESC,gBAAgBC,KAAKC,0BAU9BC,qBAAqBC,OAAQC,cAClB,cAAU,CAAC,CACdC,WAAY,kCACZC,KAAM,CACFH,cACAC,gBAEJ,kCASuBG,UAAWC,SAChCC,YAAcD,EAAEE,OAAOC,QAAQ,yCACjCF,YAAa,CACbD,EAAEI,uBACIC,eAAiB,IAAIC,iBAAQ,0CAE7Bd,KAAKE,qBACPO,YAAYM,QAAQZ,OACgB,MAApCM,YAAYM,QAAQC,YAAsB,EAAI,SAG3CC,mBAAqBC,QAAQC,IAAI,EACpC,gCAAoBZ,YACpB,wCAIJU,YAAYG,wEAAiEX,YAAYM,QAAQZ,cAAYkB,QAC7GR,eAAeS"}