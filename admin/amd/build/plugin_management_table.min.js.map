{"version": 3, "file": "plugin_management_table.min.js", "sources": ["../src/plugin_management_table.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {refreshTableContent} from 'core_table/dynamic';\nimport * as Selectors from 'core_table/local/dynamic/selectors';\nimport {call as fetchMany} from 'core/ajax';\nimport Pending from 'core/pending';\nimport {fetchNotifications} from 'core/notification';\n\nlet watching = false;\n\nexport default class {\n    /**\n     * @property {function[]} clickHandlers a list of handlers to call on click.\n     */\n    clickHandlers = [];\n\n    constructor() {\n        this.addClickHandler(this.handleStateToggle);\n        this.addClickHandler(this.handleMoveUpDown);\n        this.registerEventListeners();\n    }\n\n    /**\n     * Initialise an instance of the class.\n     *\n     * This is just a way of making it easier to initialise an instance of the class from PHP.\n     */\n    static init() {\n        if (watching) {\n            return;\n        }\n        watching = true;\n        new this();\n    }\n\n    /**\n     * Add a click handler to the list of handlers.\n     *\n     * @param {Function} handler A handler to call on a click event\n     */\n    addClickHandler(handler) {\n        this.clickHandlers.push(handler.bind(this));\n    }\n\n    /**\n     * Register the event listeners for this instance.\n     */\n    registerEventListeners() {\n        document.addEventListener('click', function(e) {\n            const tableRoot = this.getTableRoot(e);\n\n            if (!tableRoot) {\n                return;\n            }\n\n            this.clickHandlers.forEach((handler) => handler(tableRoot, e));\n        }.bind(this));\n    }\n\n    /**\n     * Get the table root from an event.\n     *\n     * @param {Event} e\n     * @returns {HTMLElement|bool}\n     */\n    getTableRoot(e) {\n        const tableRoot = e.target.closest(Selectors.main.region);\n        if (!tableRoot) {\n            return false;\n        }\n\n        return tableRoot;\n    }\n\n    /**\n     * Set the plugin state (enabled or disabled)\n     *\n     * @param {string} methodname The web service to call\n     * @param {string} plugin The name of the plugin to set the state for\n     * @param {number} state The state to set\n     * @returns {Promise}\n     */\n    setPluginState(methodname, plugin, state) {\n        return fetchMany([{\n            methodname,\n            args: {\n                plugin,\n                state,\n            },\n        }])[0];\n    }\n\n    setPluginOrder(methodname, plugin, direction) {\n        return fetchMany([{\n            methodname,\n            args: {\n                plugin,\n                direction,\n            },\n        }])[0];\n    }\n\n    /**\n     * Handle state toggling.\n     *\n     * @param {HTMLElement} tableRoot\n     * @param {Event} e\n     */\n    async handleStateToggle(tableRoot, e) {\n        const stateToggle = e.target.closest('[data-action=\"togglestate\"][data-toggle-method]');\n        if (stateToggle) {\n            e.preventDefault();\n            const pendingPromise = new Pending('core_table/dynamic:togglestate');\n\n            await this.setPluginState(\n                stateToggle.dataset.toggleMethod,\n                stateToggle.dataset.plugin,\n                stateToggle.dataset.state === '1' ? 0 : 1\n            );\n\n            const [updatedRoot] = await Promise.all([\n                refreshTableContent(tableRoot),\n                fetchNotifications(),\n            ]);\n\n            // Refocus on the link that as pressed in the first place.\n            updatedRoot.querySelector(`[data-action=\"togglestate\"][data-plugin=\"${stateToggle.dataset.plugin}\"]`).focus();\n            pendingPromise.resolve();\n        }\n    }\n\n    async handleMoveUpDown(tableRoot, e) {\n        const actionLink = e.target.closest('[data-action=\"move\"][data-method][data-direction]');\n        if (!actionLink) {\n            return;\n        }\n\n        e.preventDefault();\n\n        const pendingPromise = new Pending('core_table/dynamic:processAction');\n\n        await this.setPluginOrder(\n            actionLink.dataset.method,\n            actionLink.dataset.plugin,\n            actionLink.dataset.direction === 'up' ? -1 : 1,\n        );\n\n        const [updatedRoot] = await Promise.all([\n            refreshTableContent(tableRoot),\n            fetchNotifications(),\n        ]);\n\n        // Refocus on the link that as pressed in the first place.\n        const exactMatch = updatedRoot.querySelector(\n            `[data-action=\"move\"][data-plugin=\"${actionLink.dataset.plugin}\"][data-direction=\"${actionLink.dataset.direction}\"]`\n        );\n        if (exactMatch) {\n            exactMatch.focus();\n        } else {\n            // The move link is not present anymore, so we need to focus on the other one.\n            updatedRoot.querySelector(`[data-action=\"move\"][data-plugin=\"${actionLink.dataset.plugin}\"]`)?.focus();\n        }\n\n        pendingPromise.resolve();\n    }\n}\n"], "names": ["watching", "constructor", "addClickHandler", "this", "handleStateToggle", "handleMoveUpDown", "registerEventListeners", "handler", "clickHandlers", "push", "bind", "document", "addEventListener", "e", "tableRoot", "getTableRoot", "for<PERSON>ach", "target", "closest", "Selectors", "main", "region", "setPluginState", "methodname", "plugin", "state", "args", "setPluginOrder", "direction", "stateToggle", "preventDefault", "pendingPromise", "Pending", "dataset", "toggle<PERSON><PERSON><PERSON>", "updatedRoot", "Promise", "all", "querySelector", "focus", "resolve", "actionLink", "method", "exactMatch"], "mappings": "gzCAqBIA,UAAW,gCAQXC,8KAFgB,SAGPC,gBAAgBC,KAAKC,wBACrBF,gBAAgBC,KAAKE,uBACrBC,uCASDN,WAGJA,UAAW,MACPG,MAQRD,gBAAgBK,cACPC,cAAcC,KAAKF,QAAQG,KAAKP,OAMzCG,yBACIK,SAASC,iBAAiB,QAAS,SAASC,SAClCC,UAAYX,KAAKY,aAAaF,GAE/BC,gBAIAN,cAAcQ,SAAST,SAAYA,QAAQO,UAAWD,MAC7DH,KAAKP,OASXY,aAAaF,SACHC,UAAYD,EAAEI,OAAOC,QAAQC,UAAUC,KAAKC,eAC7CP,YACM,EAcfQ,eAAeC,WAAYC,OAAQC,cACxB,cAAU,CAAC,CACdF,sBACAG,KAAM,CACFF,cACAC,gBAEJ,GAGRE,eAAeJ,WAAYC,OAAQI,kBACxB,cAAU,CAAC,CACdL,sBACAG,KAAM,CACFF,cACAI,wBAEJ,2BASgBd,UAAWD,SACzBgB,YAAchB,EAAEI,OAAOC,QAAQ,sDACjCW,YAAa,CACbhB,EAAEiB,uBACIC,eAAiB,IAAIC,iBAAQ,wCAE7B7B,KAAKmB,eACPO,YAAYI,QAAQC,aACpBL,YAAYI,QAAQT,OACU,MAA9BK,YAAYI,QAAQR,MAAgB,EAAI,SAGrCU,mBAAqBC,QAAQC,IAAI,EACpC,gCAAoBvB,YACpB,wCAIJqB,YAAYG,iEAA0DT,YAAYI,QAAQT,cAAYe,QACtGR,eAAeS,kCAIA1B,UAAWD,SACxB4B,WAAa5B,EAAEI,OAAOC,QAAQ,yDAC/BuB,kBAIL5B,EAAEiB,uBAEIC,eAAiB,IAAIC,iBAAQ,0CAE7B7B,KAAKwB,eACPc,WAAWR,QAAQS,OACnBD,WAAWR,QAAQT,OACc,OAAjCiB,WAAWR,QAAQL,WAAsB,EAAI,SAG1CO,mBAAqBC,QAAQC,IAAI,EACpC,gCAAoBvB,YACpB,wCAIE6B,WAAaR,YAAYG,0DACUG,WAAWR,QAAQT,qCAA4BiB,WAAWR,QAAQL,2CAEvGe,WACAA,WAAWJ,sCAGXJ,YAAYG,0DAAmDG,WAAWR,QAAQT,sEAAae,QAGnGR,eAAeS"}