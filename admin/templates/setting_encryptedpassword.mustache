{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_encryptedpassword

    Admin encrypted password template.

    Context variables required for this template:
    * name - form element name
    * set - whether it is set or empty
    * id - element id

    Example context (json):
    {
        "name": "test",
        "id": "test0",
        "set": true
    }
}}
<div class="core_admin_encryptedpassword" data-encryptedpasswordid="{{ id }}"
        {{#novalue}}data-novalue="y"{{/novalue}}>
    {{#set}}
        <span>{{# str }} encryptedpassword_set, admin {{/ str }}</span>
    {{/set}}
    {{^set}}
        <a href="#" title="{{# str }} encryptedpassword_edit, admin {{/ str }}">
            <span>{{# str }} novalueclicktoset, form {{/ str }}</span>
            {{# pix }} t/passwordunmask-edit, core, {{# str }} passwordunmaskedithint, form {{/ str }}{{/ pix }}
        </a>
    {{/set}}
    <input style="display: none" type="password" name="{{name}}" disabled>
    {{!
        Using buttons instead of links here allows them to be connected to the label, so the button
        works if you click the label.
    }}
    {{#set}}
        <button type="button" id="{{id}}" title="{{# str }} encryptedpassword_edit, admin {{/ str }}" class="btn btn-link" data-editbutton>
            {{# pix }} t/passwordunmask-edit, core, {{/ pix }}
        </button>
    {{/set}}
    <button type="button" style="display: none" title="{{# str }} cancel {{/ str }}" class="btn btn-link" data-cancelbutton>
        <i class="icon fa fa-times"></i>
    </button>
</div>

{{#js}}
require(['core_form/encryptedpassword'], function(encryptedpassword) {
    new encryptedpassword.EncryptedPassword("{{ id }}");
});
{{/js}}
