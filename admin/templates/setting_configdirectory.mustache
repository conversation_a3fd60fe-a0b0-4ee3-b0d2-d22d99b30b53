{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configdirectory

    Admin directory setting template.

    Context variables required for this template:
    * name - form element name
    * id - element id
    * size - size of the field
    * readonly - Make the field readonly
    * value - value
    * showvalidity - Show a green check if the path is readable
    * valid - True if the path is readable

    Example context (json):
    {
        "name": "test",
        "value": "/my-super-secret-path/",
        "id": "test0",
        "readonly": false,
        "showvalidity": true,
        "valid": false
    }
}}
{{>core_admin/setting_configfile}}
