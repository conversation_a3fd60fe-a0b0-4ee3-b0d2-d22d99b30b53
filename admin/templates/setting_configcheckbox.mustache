{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configcheckbox

    Admin checkbox setting template.

    Context variables required for this template:
    * name - form element name
    * no - no value
    * value - yes value
    * id - element id
    * checked - boole
    * readonly - bool

    Example context (json):
    {
        "name": "test",
        "no": "False",
        "value": "True",
        "id": "test0",
        "checked": "checked",
        "readonly": false
    }
}}
<div class="form-checkbox defaultsnext">
    <input type="hidden" name="{{name}}" value="{{no}}">
    <input {{#readonly}}disabled{{/readonly}} type="checkbox" name="{{name}}" value="{{value}}" id="{{id}}" {{#checked}}checked{{/checked}}>
</div>
