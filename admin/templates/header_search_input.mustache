{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/header_search_input

    Admin search input for page header.

    Example context (json):
    {
        "action": "http://moodle.local/admin/search.php",
        "query": "themedesigner"
    }
}}
<div class="simplesearchform d-flex justify-content-end">
  <form autocomplete="off" action="{{action}}" method="get" accept-charset="utf-8" class="mform form-inline simplesearchform">
    <div class="input-group">
      <label for="searchinput-{{uniqid}}">
        <span class="sr-only">{{#str}} search, core {{/str}}</span>
      </label>
      <input type="text"
             id="searchinput-{{uniqid}}"
             class="form-control"
             placeholder="{{#str}} search, core {{/str}}"
             aria-label="{{#str}} search, core {{/str}}"
             name="query"
             data-region="input"
             autocomplete="off"
             {{#query}}value="{{.}}"{{/query}}
      >
      <div class="input-group-append">
        <button type="submit" class="btn btn-primary search-icon">
          {{#pix}} a/search, core {{/pix}}
          <span class="sr-only">{{#str}} search, core {{/str}}</span>
        </button>
      </div>
    </div>
  </form>
</div>
