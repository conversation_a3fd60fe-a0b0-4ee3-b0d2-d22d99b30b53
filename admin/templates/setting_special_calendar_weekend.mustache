{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_special_calendar_weekend

    Admin special_calendar_weekend setting template.

    Context variables required for this template:
    * name - form element name
    * id - form element id
    * days - list of days containing index, label, checked

    Example context (json):
    {
        "name": "test",
        "id": "i1",
        "days": [
            { "index": 0, "label": "Monday", "checked": true },
            { "index": 0, "label": "Tuesday", "checked": false }
        ]
    }
}}
<table>
    <thead>
        <tr>
            <input type="hidden" name="{{name}}[xxxxx]" value="1">
            {{#days}}
                <td>
                    <label for="{{id}}{{index}}">{{label}}</label>
                </td>
            {{/days}}
        </tr>
    </thead>
    <tbody>
        <tr>
            {{#days}}
                <td>
                    <input type="checkbox" class="form-checkbox" id="{{id}}{{index}}" name="{{name}}[]" value="{{index}}" {{#checked}}checked{{/checked}}>
                </td>
            {{/days}}
        </tr>
    </tbody>
</table>
