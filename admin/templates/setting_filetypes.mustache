{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_filetypes

    Renders the admin_setting_filetypes setting element.

    Context variables required for this template:
    * id - element id
    * name - form element name
    * value - element value
    * descriptions - data for the core_form/filetypes-descriptions template

    Example context (json):
    {
        "id": "test0",
        "name": "test",
        "value": ".jpg,.gif",
        "descriptions": {
            "hasdescriptions": true,
            "descriptions": [
                {
                    "description": "Image (JPEG)",
                    "extensions": ".jpeg .jpe .jpg"
                },
                {
                    "description": "Image (GIF)",
                    "extensions": ".gif"
                }
            ]
        }
    }
}}
<div class="form-text defaultsnext">
    <input type="text" name="{{name}}" value="{{value}}" size="30" id="{{id}}" class="text-ltr">
    <span data-filetypesbrowser="{{id}}"></span>
    <div data-filetypesdescriptions="{{id}}">{{#descriptions}}{{>core_form/filetypes-descriptions}}{{/descriptions}}</div>
</div>
