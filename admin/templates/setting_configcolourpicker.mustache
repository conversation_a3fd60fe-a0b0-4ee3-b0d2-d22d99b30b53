{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configcolourpicker

    Admin setting colour picker template.

    Context variables required for this template:
    * icon - optional icon context (see pix_icon)
    * name - element name
    * id - element id
    * value - element value
    * haspreviewconfig - show preview of selected color
    * readonly - bool

    Example context (json):
    {
        "icon": false,
        "name": "name0",
        "id": "id0",
        "value": "#555655",
        "readonly": false,
        "haspreviewconfig": false
    }
}}
{{!
    Setting configcolourpicker.
}}
<div class="form-colourpicker defaultsnext">
    <div class="admin_colourpicker clearfix">
        {{#icon}}
            {{>core/pix_icon}}
        {{/icon}}
    </div>
    <input type="text" name="{{name}}" id="{{id}}" value="{{value}}" size="12" class="form-control text-ltr" {{#readonly}}disabled{{/readonly}}>
    {{#haspreviewconfig}}
        <input type="button" id="{{id}}_preview" value="{{#cleanstr}}preview{{/cleanstr}}" class="admin_colourpicker_preview">
    {{/haspreviewconfig}}
</div>
