{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configtextarea

    Admin textarea setting template.

    Context variables required for this template:
    * name - form element name
    * id - element id
    * rows - number of rows
    * cols - number of cols
    * value - default value
    * forceltr - always display as ltr

    Example context (json):
    {
        "name": "test",
        "cols": "30",
        "rows": "3",
        "value": "Excellent day for putting Slinkies on an escalator.",
        "readonly": false,
        "id": "test0"
    }
}}
{{!
    Setting configtextarea.
}}
<div class="form-textarea">
    <textarea {{#readonly}}disabled{{/readonly}} rows="{{rows}}" cols="{{cols}}" id="{{id}}" name="{{name}}" spellcheck="true" class="form-control {{#forceltr}}text-ltr{{/forceltr}}">{{value}}</textarea>
</div>
