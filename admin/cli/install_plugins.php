<?php
/**
 * CLI script for automatic plugin installation during deployment
 *
 * This script automatically downloads and installs plugins defined in plugins_config.json
 * It's designed to run during deployment to ensure required plugins are available.
 *
 * @package    core
 * @subpackage cli
 * @copyright  2024 Learning Flix
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require(__DIR__.'/../../config.php');
require_once($CFG->libdir.'/clilib.php');
require_once($CFG->libdir.'/adminlib.php');

// Get cli options.
list($options, $unrecognized) = cli_get_params(
    array(
        'help' => false,
        'config' => '',
        'force' => false,
        'verbose' => false,
    ),
    array(
        'h' => 'help',
        'c' => 'config',
        'f' => 'force',
        'v' => 'verbose'
    )
);

if ($unrecognized) {
    $unrecognized = implode("\n  ", $unrecognized);
    cli_error(get_string('cliunknowoption', 'admin', $unrecognized));
}

if ($options['help']) {
    $help = "
Automatic plugin installation script for deployment.

Options:
-h, --help              Print out this help
-c, --config=PATH       Path to plugins configuration file (default: plugins_config.json)
-f, --force             Force reinstallation of existing plugins
-v, --verbose           Verbose output

Example:
\$sudo -u www-data /usr/bin/php admin/cli/install_plugins.php
\$sudo -u www-data /usr/bin/php admin/cli/install_plugins.php --config=custom_plugins.json --verbose
";

    echo $help;
    die;
}

// Default config file path
$config_file = $options['config'] ?: $CFG->dirroot . '/plugins_config.json';

if (!file_exists($config_file)) {
    cli_error("Configuration file not found: $config_file");
}

// Load plugin configuration
$config_content = file_get_contents($config_file);
$plugins_config = json_decode($config_content, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    cli_error("Invalid JSON in configuration file: " . json_last_error_msg());
}

if (!isset($plugins_config['plugins']) || !is_array($plugins_config['plugins'])) {
    cli_error("Configuration file must contain a 'plugins' array");
}

/**
 * Download and extract plugin from URL
 */
function download_and_extract_plugin($url, $destination, $verbose = false) {
    global $CFG;
    
    if ($verbose) {
        cli_writeln("Downloading plugin from: $url");
    }
    
    // Create temporary file
    $temp_file = tempnam(sys_get_temp_dir(), 'moodle_plugin_');
    
    // Download file
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Moodle Plugin Installer');
    
    $data = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200 || $data === false) {
        unlink($temp_file);
        throw new Exception("Failed to download plugin. HTTP Code: $http_code");
    }
    
    file_put_contents($temp_file, $data);
    
    if ($verbose) {
        cli_writeln("Extracting plugin to: $destination");
    }
    
    // Extract ZIP file
    $zip = new ZipArchive();
    if ($zip->open($temp_file) === TRUE) {
        // Get the first folder name in the ZIP (plugin root folder)
        $first_entry = $zip->getNameIndex(0);
        $plugin_folder = explode('/', $first_entry)[0];
        
        // Extract to temporary location first
        $temp_extract = sys_get_temp_dir() . '/moodle_plugin_extract_' . uniqid();
        mkdir($temp_extract, 0755, true);
        
        $zip->extractTo($temp_extract);
        $zip->close();
        
        // Move from temp location to final destination
        $source = $temp_extract . '/' . $plugin_folder;
        if (is_dir($source)) {
            if (is_dir($destination)) {
                // Remove existing directory if force is enabled
                exec("rm -rf " . escapeshellarg($destination));
            }
            
            // Create parent directory if it doesn't exist
            $parent_dir = dirname($destination);
            if (!is_dir($parent_dir)) {
                mkdir($parent_dir, 0755, true);
            }
            
            rename($source, $destination);
        }
        
        // Cleanup
        exec("rm -rf " . escapeshellarg($temp_extract));
        unlink($temp_file);
        
        return true;
    } else {
        unlink($temp_file);
        throw new Exception("Failed to extract ZIP file");
    }
}

/**
 * Check if plugin is already installed
 */
function is_plugin_installed($plugin_type, $plugin_name) {
    $plugin_manager = core_plugin_manager::instance();
    $plugin_info = $plugin_manager->get_plugin_info($plugin_type . '_' . $plugin_name);
    return $plugin_info !== null;
}

// Process each plugin
$installed_count = 0;
$skipped_count = 0;
$error_count = 0;

cli_writeln("Starting plugin installation process...");

foreach ($plugins_config['plugins'] as $plugin) {
    if (!isset($plugin['name']) || !isset($plugin['type']) || !isset($plugin['url'])) {
        cli_writeln("ERROR: Plugin configuration missing required fields (name, type, url)");
        $error_count++;
        continue;
    }
    
    $plugin_name = $plugin['name'];
    $plugin_type = $plugin['type'];
    $plugin_url = $plugin['url'];
    $plugin_component = $plugin_type . '_' . $plugin_name;
    
    cli_writeln("Processing plugin: $plugin_component");
    
    // Check if plugin is already installed
    if (is_plugin_installed($plugin_type, $plugin_name) && !$options['force']) {
        cli_writeln("  SKIPPED: Plugin $plugin_component is already installed");
        $skipped_count++;
        continue;
    }
    
    try {
        // Determine destination path based on plugin type
        $destination = $CFG->dirroot . '/' . $plugin_type . '/' . $plugin_name;
        
        // Download and extract plugin
        download_and_extract_plugin($plugin_url, $destination, $options['verbose']);
        
        cli_writeln("  SUCCESS: Plugin $plugin_component installed successfully");
        $installed_count++;
        
    } catch (Exception $e) {
        cli_writeln("  ERROR: Failed to install plugin $plugin_component: " . $e->getMessage());
        $error_count++;
    }
}

cli_writeln("\nPlugin installation completed:");
cli_writeln("  Installed: $installed_count");
cli_writeln("  Skipped: $skipped_count");
cli_writeln("  Errors: $error_count");

if ($installed_count > 0) {
    cli_writeln("\nNote: Run 'php admin/cli/upgrade.php --non-interactive' to complete plugin installation.");
}

exit($error_count > 0 ? 1 : 0);
