{"version": 3, "file": "potential-contexts.min.js", "sources": ["../src/potential-contexts.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Potential contexts selector module.\n *\n * @module     tool_analytics/potential-contexts\n * @copyright  2019 David <PERSON>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\ndefine(['jquery', 'core/ajax'], function($, Ajax) {\n\n    return /** @alias module:tool_analytics/potential-contexts */ {\n\n        processResults: function(selector, results) {\n            var contexts = [];\n            if ($.isArray(results)) {\n                $.each(results, function(index, context) {\n                    contexts.push({\n                        value: context.id,\n                        label: context.name\n                    });\n                });\n                return contexts;\n\n            } else {\n                return results;\n            }\n        },\n\n        transport: function(selector, query, success, failure) {\n            var promise;\n\n            let modelid = $(selector).attr('modelid') || null;\n            promise = Ajax.call([{\n                methodname: 'tool_analytics_potential_contexts',\n                args: {\n                    query: query,\n                    modelid: modelid\n                }\n            }]);\n\n            promise[0].then(success).fail(failure);\n        }\n\n    };\n\n});\n"], "names": ["define", "$", "Ajax", "processResults", "selector", "results", "contexts", "isArray", "each", "index", "context", "push", "value", "id", "label", "name", "transport", "query", "success", "failure", "modelid", "attr", "call", "methodname", "args", "then", "fail"], "mappings": ";;;;;;;AAuBAA,2CAAO,CAAC,SAAU,cAAc,SAASC,EAAGC,YAEsB,CAE1DC,eAAgB,SAASC,SAAUC,aAC3BC,SAAW,UACXL,EAAEM,QAAQF,UACVJ,EAAEO,KAAKH,SAAS,SAASI,MAAOC,SAC5BJ,SAASK,KAAK,CACVC,MAAOF,QAAQG,GACfC,MAAOJ,QAAQK,UAGhBT,UAGAD,SAIfW,UAAW,SAASZ,SAAUa,MAAOC,QAASC,aAGtCC,QAAUnB,EAAEG,UAAUiB,KAAK,YAAc,KACnCnB,KAAKoB,KAAK,CAAC,CACjBC,WAAY,oCACZC,KAAM,CACFP,MAAOA,MACPG,QAASA,YAIT,GAAGK,KAAKP,SAASQ,KAAKP,UAKzC"}