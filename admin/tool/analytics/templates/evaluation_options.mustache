{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_analytics/evaluation_options

    Evaluation selector.

    The purpose of this template is to render the evaluation mode options.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Example context (json):
    {
        "trainedexternally": "1",
        "timesplittingmethods": [
            {
                "id": "ou",
                "name": "Quarters"
            }, {
                "id": "yeah",
                "name": "Tenths"
            }
        ]
    }
}}

{{#trainedexternally}}
    <div class="box mb-4">{{#str}} evaluationmodeinfo, tool_analytics {{/str}}</div>

    <div class="custom-control custom-radio">
        <input class="custom-control-input" type="radio" name="evaluationmode" id="id-mode-trainedmodel" value="trainedmodel" checked>
        <label class="custom-control-label" for="id-mode-trainedmodel">{{#str}} evaluationmodetrainedmodel, tool_analytics {{/str}}</label>
    </div>

    <div class="custom-control custom-radio">
        <input class="custom-control-input" type="radio" name="evaluationmode" id="id-mode-configuration" value="configuration">
        <label class="custom-control-label" for="id-mode-configuration">{{#str}} evaluationmodeconfiguration, tool_analytics {{/str}}</label>
    </div>
{{/trainedexternally}}

{{! Hidden by default if #trainedexternally as the default option is trainedmodel in this case.}}
<div id="id-evaluation-timesplitting-container" class="mt-3 {{#trainedexternally}}hidden{{/trainedexternally}}">
    {{#str}} selecttimesplittingforevaluation, tool_analytics {{/str}}
    <div>
        <select id="id-evaluation-timesplitting" name="timesplitting" class="custom-select mt-3">
            {{#timesplittingmethods}}
                <option value="{{id}}">{{text}}</option>
            {{/timesplittingmethods}}
        </select>
    </div>
</div>


{{#js}}
    require(['jquery'], function($) {
        $("input[name='evaluationmode']:radio").change(function() {
            if ($(this).val() == 'configuration') {
                $('#id-evaluation-timesplitting-container').show();
            } else {
                $('#id-evaluation-timesplitting-container').hide();
            }
        });
    });
{{/js}}