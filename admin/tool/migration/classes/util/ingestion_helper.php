<?php namespace tool_migration\util;

use DateTimeZone;
use DateTime;
use local_ssystem\constants\custom_course_fields;

require_once($CFG->libdir . '/weblib.php');

class ingestion_helper {

    public static function get_date_time_zone() : DateTimeZone {
        return new DateTimeZone('America/Sao_Paulo');
    }

    public static function str_to_timestamp(?string $string) : int {
        if(empty($string)){
            return 0;
        }

        try {
            $date = DateTime::createFromFormat('Y-m-d H:i:s.u', $string, static::get_date_time_zone());
            return $date->getTimestamp();
        } catch (\Throwable $th) {
            return 0;
        }
    }

    public static function str_to_profile_field_datetime($string) : string {
                return (new DateTime($string))->getTimestamp();
        if (empty($string)) {
            return '';
        }
        $date = DateTime::createFromFormat(
            'Y-m-d H:i:s.u',
            $string,
            static::get_date_time_zone()
        );
        if (!$date) {
            // Fallback: try without microseconds
            $date = DateTime::createFromFormat(
                'Y-m-d H:i:s',
                $string,
                static::get_date_time_zone()
            );
            if (!$date) {
                return '';
            }
        }

        return $date->format('Y-m-d-H-i-s');
    }

    public static function only_numbers(?string $numbers) : string {
        return preg_replace('/\D/', '', $numbers ?? '');
    }

    public static function format_phone(?string $phone) : string {
        $phone = preg_replace('/\D/', '', $phone ?? '');
        return "+55$phone";
    }

    public static function  split_fullname(string $fullname): object {
        $fullname = mb_convert_case(trim($fullname), MB_CASE_TITLE, 'UTF-8');
        $parts = preg_split('/\s+/', trim($fullname));
        
        if (empty($parts)) {
            return (object)[
                'firstname'  => '',
                'lastname'   => ''
            ];
        }
        
        $firstname = array_shift($parts);       
        $lastname = $parts ? implode(' ', $parts) : '';
        
        return (object)[
            'firstname'  => $firstname,
            'lastname'   => $lastname
        ];
    }

    public static function str_to_bool(?string $string) : bool {
        if(empty($string)){
            return false;
        }

        return match (mb_strtolower(trim($string))) {
            'true' => true,
            'false' => false,
            's' => true,
            'n' => false,
            default => false,
        };
    }

    public static function is_null($value) : bool {
        return strtoupper(trim($value)) == "NULL";
    }

    public static function format_email($email) : string {
        if(is_string($email)){
            $email = trim($email);
        }

        if(empty($email) || self::is_null($email)){
            return '';
        }

        return clean_param($email, PARAM_EMAIL);
    }

    public static function generate_fake_email($seed) : string {
        return md5($seed) . "@invalid.com";
    }

    public static function remove_html_and_trim(?string $input) : ?string {
        if($input === null){
            return null;
        }

        $input = mb_strtolower(trim(html_to_text($input)));
        return trim($input, ' .'); // WTF
    }

    public static function parse_complexity_level(?string $input) : string {
        return match (self::remove_html_and_trim($input)) {
            'básica', 'basica', 'baixa', 'básico', 'basico' => custom_course_fields::COMPLEXITY_BASIC,
            'intermediário', 'intermediária', 'intermediario', 'intermediaria' => custom_course_fields::COMPLEXITY_INTERMEDIATE,
            'alta', 'avançado', 'alto', 'avançado' => custom_course_fields::COMPLEXITY_ADVANCED,
            default => '',
        };
    }

    public static function parse_solution_format(?string $input) : string {
        return match (self::remove_html_and_trim($input)) {
            'curso online' => 'Curso online',
            'eventos corporativos presenciais' => 'Eventos corporativos presenciais',
            'curso presencial' => 'Curso presencial',
            'eventos corporativos virtuais' => 'Eventos corporativos virtuais',
            'curso semipresencial' => 'Curso semipresencial',
            'jogos' => 'Jogos',
            'jornada de aprendizagem' => 'Jornada de aprendizagem',
            'trilha de aprendizagem' => 'Trilha de aprendizagem',
            'certificação de conhecimento' => 'Certificação de Conhecimento',
            'recursos audiovisuais' => 'Recursos multimídia',
            default => '',
        };
    }

    public static function parse_workload(?string $input) : int {
        if($input = self::remove_html_and_trim($input)){
            return floatval($input) * HOURSECS;
        }

        return 0;
    }
}