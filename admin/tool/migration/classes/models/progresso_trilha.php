<?php namespace tool_migration\models;

defined('MOODLE_INTERNAL') || die();

use tool_migration\util\ingestion_helper;
use Exception;
use Generator;

class progresso_trilha extends progresso_curso {
    const TABLE = 'eadtech_progresso_trilhas';

    protected ?int $userid;
    protected static array $course_instances = [];
    protected static array $enrol_instances = [];

    protected function get_enrol_instance_name() : string {
        return "LEGADO";
    }

    protected function get_source_id() : int {
        return (int) $this->raw_get('source_id');
    }

    public function get_courseid() : int {
        $key = $this->raw_get('source_codigo_da_trilha');

        if(!isset(static::$course_instances[$key])){
            static::$course_instances[$key] = $this->fetch_courseid();
        }

        return static::$course_instances[$key];
    }

    protected function fetch_courseid() : int {
        $courseid = trilha::get_instanceid_from_identifier($this->get('source_codigo_da_trilha'));

        if($courseid === null){
            throw new Exception("Curso " . $this->get('source_codigo_da_trilha') . " não encontrado");
        }

        return $courseid;
    }

    public function get_enrolid() : int {
        $key = $this->get('source_codigo_da_trilha');

        if(!isset(static::$enrol_instances[$key])){
            static::$enrol_instances[$key] = $this->fetch_enrolid();
        }

        return static::$enrol_instances[$key];
    }

    protected function fetch_enrolid() : int {
        global $DB;

        $enrol_conditions = [
            'enrol' => 'manual',
            'courseid' => $this->get_courseid(),
            'name' => $this->get_enrol_instance_name(),
        ];

        if($enrolid = $DB->get_field('enrol', 'id', $enrol_conditions)){
            return $enrolid;
        }

        $enrol_conditions['status'] = 0;
        $enrol_conditions['timecreated'] = time();
        $enrol_conditions['timemodified'] = time();

        return $DB->insert_record('enrol', $enrol_conditions);
    }


    public static function define_identifier(): string {
        return 'source_id';
    }

    public function is_completed() : bool {
        $progressstatus = trim($this->get('source_status_de_progresso') ?? '');
        return in_array($progressstatus, [
            static::STATUS_COMPLETED,
            static::STATUS_APPROVED,
            static::STATUS_COMPLETED2,
            static::STATUS_APPROVED2,
        ]);
    }

    protected static function define_model_properties(): array {
        return [
            'source_id' => [
                'type' => PARAM_INT,
            ],
            'source_codigo_da_trilha' => [
                'type' => PARAM_TEXT,
            ],
            'source_codigo_da_turma' => [
                'type' => PARAM_TEXT,
            ],
            'source_codaluno' => [
                'type' => PARAM_TEXT,
            ],
            'source_data_de_matricula' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_porcent_conclusao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_porcent_aproveitamento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_prazo_de_acesso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_origem_da_matricula' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_inicio' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_fim' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_status_da_matricula' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_status_de_progresso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_cancelamento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_motivo_do_cancelamento' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_nota_do_usuario' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_do_primeiro_acesso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_do_ultimo_acesso' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_conclusao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
            'source_data_de_modificacao' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public function get_idnumber(): string {
        return 'LEGADO-' . $this->get('source_id');
    }

    public function to_legacy_record() : object {
        global $CFG;

        $record = (object)[];
               
        return $record;
    }

    public function to_user_enrolment() : object {
        $record = (object)[
            'status' => 0,
            'userid' => $this->get_userid(),
            'enrolid' => $this->get_enrolid(),
            'timestart' => ingestion_helper::str_to_timestamp($this->get('source_data_de_inicio')),
            'timeend' => ingestion_helper::str_to_timestamp($this->get('source_data_de_fim')),
            'timecreated' => ingestion_helper::str_to_timestamp($this->get('source_data_de_matricula')),
            'timemodified' => time(),
        ];

        if($id = $this->get('user_enrolment_id')){
            $record->id = $id;
        }
               
        return $record;
    }

    public function to_course_completion() : object {
        $record = (object)[
            'userid' => $this->get_userid(),
            'course' => $this->get_courseid(),
            'timeenrolled' => ingestion_helper::str_to_timestamp($this->get('source_data_de_matricula')),
            'timestarted' => ingestion_helper::str_to_timestamp($this->get('source_data_do_primeiro_acesso')),
            'timecompleted' => ingestion_helper::str_to_timestamp($this->get('source_data_de_conclusao')),
        ];

        if($id = $this->get('course_completion_id')){
            $record->id = $id;
        }
               
        return $record;
    }





    public static function upsert_from_csv(array $data) : bool {
        if(!empty($data['source_data_de_matricula']) && strlen($data['source_data_de_matricula']) > 28){
            $data['source_data_de_matricula'] = substr($data['source_data_de_matricula'], 0, 28);
        }

        if(!empty($data['source_data_de_inicio']) && strlen($data['source_data_de_inicio']) > 28){
            $data['source_data_de_inicio'] = substr($data['source_data_de_inicio'], 0, 28);
        }

        if(!empty($data['source_data_de_fim']) && strlen($data['source_data_de_fim']) > 28){
            $data['source_data_de_fim'] = substr($data['source_data_de_fim'], 0, 28);
        }

        if(!empty($data['source_data_de_cancelamento']) && strlen($data['source_data_de_cancelamento']) > 28){
            $data['source_data_de_cancelamento'] = substr($data['source_data_de_cancelamento'], 0, 28);
        }

        if(!empty($data['source_data_do_primeiro_acesso']) && strlen($data['source_data_do_primeiro_acesso']) > 28){
            $data['source_data_do_primeiro_acesso'] = substr($data['source_data_do_primeiro_acesso'], 0, 28);
        }

        if(!empty($data['source_data_do_ultimo_acesso']) && strlen($data['source_data_do_ultimo_acesso']) > 28){
            $data['source_data_do_ultimo_acesso'] = substr($data['source_data_do_ultimo_acesso'], 0, 28);
        }

        if(!empty($data['source_data_de_conclusao']) && strlen($data['source_data_de_conclusao']) > 28){
            $data['source_data_de_conclusao'] = substr($data['source_data_de_conclusao'], 0, 28);
        }

        if(!empty($data['source_data_de_modificacao']) && strlen($data['source_data_de_modificacao']) > 28){
            $data['source_data_de_modificacao'] = substr($data['source_data_de_modificacao'], 0, 28);
        }

        if(!empty($data['source_prazo_de_acesso']) && strlen($data['source_prazo_de_acesso']) > 28){
            $data['source_prazo_de_acesso'] = substr($data['source_prazo_de_acesso'], 0, 28);
        }
        
        return parent::upsert_from_csv($data);
    }
}
