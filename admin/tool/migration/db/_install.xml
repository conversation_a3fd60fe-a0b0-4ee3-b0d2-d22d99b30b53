<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="admin/tool/migration/db" VERSION="20250522" COMMENT="XMLDB file for Moodle admin/tool/migration"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="eadtech_estudantes" COMMENT="Dados de migração da EADTECH">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_nome" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_email" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_estado" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_sexo" TYPE="char" LENGTH="20" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nascimento" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_escolaridade" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_estadocivil" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_renda" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datacadastro" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_cnpj_lista" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_cargo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nivelocupacional" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_cpf" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_emailsecundario" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_celular" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_campolivre3" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_perfil" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_status" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_filial" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_dataadmissao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_dataexpiracao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_dataalteracao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nomesocial" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_pais" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_funcao" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_degreed_usuarioativo" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_ufsebrae" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datamodificacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="custom_data" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="legacyid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_codaluno_idx" UNIQUE="false" FIELDS="source_codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_cursos" COMMENT="Dados de migração da EADTECH - Cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_disponivel" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_idcurso" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nome" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nomecursomenu" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_status" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_prazo" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datacriacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datamoficacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_cursopresencial" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_frequencia" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_media" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_rematricula" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_keywords" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_cargahoraria" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_descricao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_tipo_solucao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_apresentacao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_objetivos" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_conteudo_programatico" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nivel_de_complexidade" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_termo_de_aceite" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_ficha_tecnica" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_requisitos" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_criterios_de_avaliacao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_publico" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_area_subarea" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="legacyid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_codcurso_idx" UNIQUE="false" FIELDS="source_codcurso"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_segmentos" COMMENT="Dados de migração da EADTECH - Segmentos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codsegmento" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_nomesegmento" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_criado" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_modificado" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_codusuariogestor" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_codsegmento_idx" UNIQUE="true" FIELDS="source_codsegmento"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_usuarios_segmento" COMMENT="Dados de migração da EADTECH - Relacionamento entre usuarios e segmentos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codsegmento" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_tipo" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_principal" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_codaluno_codsegmento_idx" UNIQUE="true" FIELDS="source_codsegmento, source_codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_trilhas" COMMENT="Dados de migração da EADTECH - Trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_disponivel" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nome" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_status" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_prazo" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_criacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_modificacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_frequencia" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_media" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_permite_rematricula" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_keywords" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_carga_horaria_minima" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_descricao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_tipo_solucao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_apresentacao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_objetivos" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_conteudo_programatico" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nivel_de_complexidade" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_termo_de_aceite" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_ficha_tecnica" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_requisitos" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_criterios_de_avaliacao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_publico" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_area_subarea" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="legacyid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_id_idx" UNIQUE="false" FIELDS="source_id"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_gestores" COMMENT="Dados de migração dos gestores EADTECH">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codusuariogestor" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_nomecompleto" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_login" TYPE="char" LENGTH="120" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_email" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datadecriacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datamodificacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_status" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_trocarsenha" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_acessarmensagens" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_webaula" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_ehelpdesk" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_acessobiometrico" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_tran_userid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_corpodocente" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_codusuariogestor_idx" UNIQUE="false" FIELDS="source_codusuariogestor"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_progresso_cursos" COMMENT="Dados de progresso em cursos (situações de matricula) EADTECH">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codsituacaoalunocurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_datamatricula" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_percconclusao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_percaproveitamento" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_prazodeacesso" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_origemmatricula" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datainicio" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datafim" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_statusmatricula" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_progressstatus" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_cancelamento" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_motivo_do_cancelamento" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nota_do_usuario" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_do_primeiro_acesso" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_do_ultimo_acesso" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_conclusao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_modificacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="course_completion_id" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="user_enrolment_id" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="legacyid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codsituacaoalunocurso_idx" UNIQUE="false" FIELDS="source_codsituacaoalunocurso"/>
        <INDEX NAME="idx_prog_prev_comp" UNIQUE="false" FIELDS="source_codcurso,source_codaluno,course_completion_id,source_codsituacaoalunocurso"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_progresso_trilhas" COMMENT="Dados de progresso em trilhas (situações de matricula) EADTECH">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codigo_da_trilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codigo_da_turma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_matricula" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="porcent_conclusao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="porcent_aproveitamento" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_prazo_de_acesso" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_origem_da_matricula" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_inicio" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_fim" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_status_da_matricula" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_status_de_progresso" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_cancelamento" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_motivo_do_cancelamento" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nota_do_usuario" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_do_primeiro_acesso" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_do_ultimo_acesso" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_conclusao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_data_de_modificacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="legacyid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="course_completion_id" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="user_enrolment_id" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_id_idx" UNIQUE="false" FIELDS="source_id"/>
        <INDEX NAME="idx_prog_prev_comp" UNIQUE="false" FIELDS="source_codigo_da_trilha,source_codaluno,course_completion_id,source_id"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_empresas" COMMENT="Dados de migração da EADTECH - Empresas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_codempresa" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_razao_social" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_cnpj" TYPE="char" LENGTH="20" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datacadastro" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_estado" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_dataabertura" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_excluido" TYPE="char" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_dataalteracao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_fornecedorsolucoes" TYPE="char" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_nome_fantasia" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_codempresa_idx" UNIQUE="true" FIELDS="source_codempresa"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="eadtech_cursos_externos" COMMENT="EADTECH - Cursos externos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="source_id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_nomedaacao" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_descricao" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="source_instituicao" TYPE="char" LENGTH="90" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datarealizacao" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_cargahoraria" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_certificado" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_criado" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_editado" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_excluido" TYPE="char" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_certificadoguid" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="source_datarealizacaofim" TYPE="char" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="needs_update" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="locked_by" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lock_expires_at" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="source_id_idx" UNIQUE="true" FIELDS="source_id"/>
      </INDEXES>
    </TABLE>
  </TABLES>
</XMLDB>
