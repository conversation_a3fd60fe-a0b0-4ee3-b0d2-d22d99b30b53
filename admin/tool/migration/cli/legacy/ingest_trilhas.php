<?php

use tool_migration\models\trilha;

require_once(__DIR__ . '/config.php');

foreach (trilha::get_pending_legacy_upserts($limit) as $trilha) {
    try {
        $record = $trilha->to_record();
        $record->courseid = $record->instanceid;

        $legacyid = \local_legacy\models\trilha::upsert_from_migration_table_data($record);

        if($legacyid){
            $trilha->set('legacyid', $legacyid);
            $trilha->set('needs_update', 0);
            $trilha->save();

            $identifier = $trilha->get('source_codtrilha');
            mtrace("Trilha $identifier importado para o local_legacy");
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}