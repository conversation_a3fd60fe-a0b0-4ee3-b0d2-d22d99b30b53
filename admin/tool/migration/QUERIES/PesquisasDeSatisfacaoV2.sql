-- Feedback Vers<PERSON> 2: <PERSON><PERSON><PERSON><PERSON> a <PERSON> (TIPOITEM = 'TC')
-- SELECT
-- 	UU.CODESTRANGEIRO AS CODALUNO,
--     *
-- FROM FEEDBACK FB WITH (NOLOCK)
-- INNER JOIN FEED<PERSON>CKASSOCIACAO FA WITH (NOLOCK) ON (FA.CODFEEDBACK = FB.CODFEEDBACK)
-- INNER JOIN FEED<PERSON>C<PERSON>RESPOSTADEUSUARIO FRU WITH (NOLOCK) ON (FRU.CODASSOCIACAO = FA.CODASSOCIACAO)
-- INNER JOIN FEEDBACKRESPOSTADEPERGUNTA FRP WITH (NOLOCK) ON (FRU.CODRESPOSTADEPERGUNTA = FRP.CODRESPOSTADEPERGUNTA)
-- INNER JOIN FEEDBACKPERGUNTA FP WITH (NOLOCK) ON (FRP.CODPERGUNTA = FP.CODPERGUNTA)
-- INNER JOIN USUARIOUNIFICADO UU WITH (NOLOCK) ON (UU.CODUSUARIOUNIFICADO = FRU.CODUSUAR<PERSON>UN<PERSON>ICADO AND UU.TABELACAMPOESTRANGEIRO = 'Usuarios.CodAluno')
-- WHERE FA.TIPOITEM = 'TC';

SELECT
	UU.CODESTRANGEIRO AS CODALUNO,
	FRU.CODTURMA,
	FA.CODITEM AS CODCURSO,
	FB.CODFEEDBACK,
	FB.DESCRICAO,
	FB.TEXTOINTRODUCAO,
	FA.CODTOPICO,
	FP.CODPERGUNTA,
	FP.PERGUNTA,
	FP.CODCATEGORIAPERG,
	FP.TIPOPERGUNTA,
	FP.MODULO,
	FRP.RESPOSTA,
	FRU.CODRESPOSTADEPERGUNTA,
	FRU.JUSTIFICATIVA,
	FRU.DTRESPOSTA,
	FRU.CODFEEDBACKRESPOSTADEUSUARIO
FROM FEEDBACK FB WITH (NOLOCK)
INNER JOIN FEEDBACKASSOCIACAO FA WITH (NOLOCK) ON (FA.CODFEEDBACK = FB.CODFEEDBACK)
INNER JOIN FEEDBACKRESPOSTADEUSUARIO FRU WITH (NOLOCK) ON (FRU.CODASSOCIACAO = FA.CODASSOCIACAO)
INNER JOIN FEEDBACKRESPOSTADEPERGUNTA FRP WITH (NOLOCK) ON (FRU.CODRESPOSTADEPERGUNTA = FRP.CODRESPOSTADEPERGUNTA)
INNER JOIN FEEDBACKPERGUNTA FP WITH (NOLOCK) ON (FRP.CODPERGUNTA = FP.CODPERGUNTA)
INNER JOIN USUARIOUNIFICADO UU WITH (NOLOCK) ON (UU.CODUSUARIOUNIFICADO = FRU.CODUSUARIOUNIFICADO AND UU.TABELACAMPOESTRANGEIRO = 'Usuarios.CodAluno')
WHERE FA.TIPOITEM = 'TC';



-- Feedback Versão 2: Associado a Trilhas (TIPOITEM = 'TR')
-- SELECT
--     *
-- FROM FEEDBACKRESPOSTADEUSUARIO FRU WITH (NOLOCK)
-- INNER JOIN FEEDBACKASSOCIACAO FA WITH (NOLOCK) ON (FRU.CODASSOCIACAO = FA.CODASSOCIACAO)
-- INNER JOIN FEEDBACKRESPOSTADEPERGUNTA FRP WITH (NOLOCK) ON (FRU.CODRESPOSTADEPERGUNTA = FRP.CODRESPOSTADEPERGUNTA)
-- INNER JOIN FEEDBACKPERGUNTA FP WITH (NOLOCK) ON (FRP.CODPERGUNTA = FP.CODPERGUNTA)
-- INNER JOIN [dbo.LMS].TRN_Activities ACT WITH (NOLOCK) ON (
--     ACT.Id = FA.CODITEM AND
--     FA.TIPOITEM = 'TR'
-- );

SELECT
    UU.CODESTRANGEIRO AS CODALUNO,
	FB.CODFEEDBACK,
	FB.DESCRICAO,
	FB.TEXTOINTRODUCAO,
	FA.CODITEM AS CODTRILHA,
	FA.CODTOPICO,
	FP.CODPERGUNTA,
	FP.PERGUNTA,
	FP.CODCATEGORIAPERG,
	FP.TIPOPERGUNTA,
	FP.MODULO,
	FRP.RESPOSTA,
	FRU.CODRESPOSTADEPERGUNTA,
	FRU.JUSTIFICATIVA,
	FRU.DTRESPOSTA,
	FRU.CODTURMA,
	FRU.CODFEEDBACKRESPOSTADEUSUARIO
FROM FEEDBACKRESPOSTADEUSUARIO FRU WITH (NOLOCK)
INNER JOIN FEEDBACKASSOCIACAO FA WITH (NOLOCK) ON (FRU.CODASSOCIACAO = FA.CODASSOCIACAO)
INNER JOIN FEEDBACKRESPOSTADEPERGUNTA FRP WITH (NOLOCK) ON (FRU.CODRESPOSTADEPERGUNTA = FRP.CODRESPOSTADEPERGUNTA)
INNER JOIN FEEDBACKPERGUNTA FP WITH (NOLOCK) ON (FRP.CODPERGUNTA = FP.CODPERGUNTA)
INNER JOIN [dbo.LMS].TRN_Activities ACT WITH (NOLOCK) ON (
    ACT.Id = FA.CODITEM AND
    FA.TIPOITEM = 'TR'
)
INNER JOIN FEEDBACK FB WITH (NOLOCK) ON (FA.CODFEEDBACK = FB.CODFEEDBACK)
INNER JOIN USUARIOUNIFICADO UU WITH (NOLOCK) ON (UU.CODUSUARIOUNIFICADO = FRU.CODUSUARIOUNIFICADO AND UU.TABELACAMPOESTRANGEIRO = 'Usuarios.CodAluno');
