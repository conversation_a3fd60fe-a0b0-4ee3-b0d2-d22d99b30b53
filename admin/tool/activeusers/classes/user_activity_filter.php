<?php namespace tool_activeusers;

require_once($CFG->libdir . '/formslib.php');

class user_activity_filter extends \moodleform
{

    public function definition()
    {
        global $DB, $CFG, $reporturl;

        $form = $this->_form;

        $form->addElement('header', 'parameters', get_string('form-report_parameters', 'tool_activeusers'));
        $parameters = (array) $reporturl->params();


        $current_year = date("Y");
        $start_year = 2010;

        // Enable "Data Início" filter
        $options = array(
                            'startyear' => $start_year,
                            'stopyear'  => $current_year,
                            'optional'  => true
                        );
        $form->addElement('date_selector', 'timestart', get_string('timestart_filter', 'tool_activeusers'), $options);


        // Enable "Data Término" filter
        $options = array(
            'startyear' => $start_year,
            'stopyear'  => $current_year,
            'optional'  => true
        );
        $form->addElement('date_selector', 'timeend', get_string('timeend_filter', 'tool_activeusers'), $options);


        // Enable "vendas" filter
        $form->addElement('checkbox', 'consider_days', get_string('consider_days', 'tool_activeusers'));
        $form->setDefault('consider_days', 0);
        $form->addHelpButton('consider_days', 'consider_days', 'tool_activeusers');


        $actions = [
            $form->createElement('submit', 'export', get_string('export_report', 'tool_activeusers'))
        ];

        $form->addGroup($actions, 'actions', '', [' '], false);
        $form->closeHeaderBefore('actions');
    }

    public function validation($data, $files)
    {
        $errors = parent::validation($data, $files);

        return $errors;
    }

}