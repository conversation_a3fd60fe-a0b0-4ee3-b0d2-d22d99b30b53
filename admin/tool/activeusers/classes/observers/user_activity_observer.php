<?php namespace tool_activeusers\observers;

abstract class user_activity_observer{
    
    const TABLE = 'tool_activeusers_user_actvt';

    public static function navigation_hook(){
        global $DB, $USER;
        self::proccess_user_activity($USER->id);

    }


    public static function event_hook($event){
        global $DB, $USER;
        self::proccess_user_activity($USER->id);
    }


    protected static function proccess_user_activity($userid){
        global $DB;


        $record = [
            'year' => date("Y", time()),
            'month' => date("m", time()),
            'userid' => $userid,
        ];
        
        // if($DB->get_record(self::TABLE, $record)){
        //     return;
        // }

        if(!empty($DB->get_records(self::TABLE, $record, '', '*', 0, 1))){
            return; 
        } // It might get some duplicated records, because of the log processing, but it is unlikely

        $record['timecreated'] = time();

        $DB->insert_record(self::TABLE, $record);
    }
}