<?php namespace tool_activeusers\tasks;

use \tool_activeusers\observers\user_activity_observer;

class initial_dump_adhoc_task extends \core\task\adhoc_task {
    public function execute() {

        // Get the custom data.
        // $data = $this->get_custom_data();

        $this->proccess_from_old_logs();

        set_config('finishedinstalation', 1, 'tool_activeusers');

    }



    /**
     * Called during installation.
     * 
     * It uses the default log to extract
     * user activity information.
     *
     * @return void
     */
    public function proccess_from_old_logs(){
        global $CFG, $DB;

        \core_php_time_limit::raise();
        raise_memory_limit(MEMORY_UNLIMITED);

        // First, we empty the 'tool_activeusers_user_actvt' table;
        // $DB->execute("TRUNCATE TABLE {tool_activeusers_user_actvt}");


        /* $sql = "SELECT 
                    DATE_FORMAT(FROM_UNIXTIME(timecreated), '%m') AS month,
                    DATE_FORMAT(FROM_UNIXTIME(timecreated), '%Y') AS `year`,
                    IF(realuserid IS NULL, userid, realuserid) AS userid,
                    MIN(timecreated) AS timecreated
                FROM mdl_logstore_standard_log
                WHERE origin = 'web'
                AND action IN ('loggedin', 'viewed')
                        
                GROUP BY month, `year`, userid"; */ //It gets an warning from MySQL
        
        $sql = "SELECT
                    inner_query.`month`,
                    inner_query.`year`,
                    inner_query.userid,
                    MIN(timecreated) AS timecreated
                FROM (
                    SELECT 
                        DATE_FORMAT(FROM_UNIXTIME(timecreated), '%m') AS `month`,
                        DATE_FORMAT(FROM_UNIXTIME(timecreated), '%Y') AS `year`,
                        IF(realuserid IS NULL, userid, realuserid) AS userid,
                        timecreated
                    FROM {logstore_standard_log}
                    WHERE origin = 'web'
                    AND action IN ('loggedin', 'viewed')
                ) inner_query
                        
                GROUP BY month, `year`, userid";


        $rs = $DB->get_recordset_sql($sql);

        $errors = [];

        $a_week_ago = time() - 7*24*60*60;

        // Processing:
        foreach ($rs as $record) {

            $output = "";
            try {
                if(empty($record->userid)){
                    continue;
                }
                $output = "\nUSER #{$record->userid} ({$record->month}/{$record->year})";


                // If the record is too recent, it might already been added, since this task can take a while until its executed;
                if($record->timecreated < $a_week_ago){

                    $DB->insert_record(user_activity_observer::TABLE, $record);

                }else{
                    $existing_id = $DB->get_field(user_activity_observer::TABLE, 'id', [
                        'month' => $record->month,
                        'year' => $record->year,
                        'userid' => $record->userid,
                    ]);

                    if(!$existing_id){
                        $DB->insert_record(user_activity_observer::TABLE, $record);
                    }else{
                        $record->id = $existing_id;
                        $DB->update_record(user_activity_observer::TABLE, $record);
                    }
                }
                
                
                $output .= " = [OK]" ;

            } catch (\Throwable $th) {
                $output .= " = [ERROR]" ;
                // $errors[] = $record;
            }

            mtrace($output);

        }
        $rs->close();

    }
}