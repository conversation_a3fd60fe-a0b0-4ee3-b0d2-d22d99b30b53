<?php

namespace tool_activeusers\tasks;
use tool_activeusers\user_activity_report;

defined('MOODLE_INTERNAL') || die;

/**
 *
 */
class export_user_activity_report extends \core\task\scheduled_task {

    /**
     * @var user_activity_report
     */
    private $userActivityReport;

    /**
     *
     */
    public function __construct()
    {
        $this->userActivityReport = new user_activity_report();
    }

    /**
     * @return void
     */
    public function execute()
    {
        global $CFG;
        require_once($CFG->dirroot . '/admin/tool/activeusers/settings.php');
        $pluginEnabled = get_config('tool_activeusers', 'enable');

        if ($pluginEnabled != 1) {
            echo "... Plugin disabled\n";
            return;
        }
        
        echo "... Sending activity report\n";
        $this->sendActivityReport();
    }

    /**
     * @return void
     */
    public function sendActivityReport()
    {
        $this->sendActivityReportRetroactive();
        $this->sendActivityReportDaily();
    }

    /**
     * @return void
     * @throws \dml_exception
     */
    private function sendActivityReportDaily()
    {
        global $DB;

        $numberOfDaysOfThisMonth = cal_days_in_month(CAL_GREGORIAN,date('m'),date('Y'));

        $dateRangeStart = strtotime(date("Y-m-01 00:00:00"));
        $dateRangeFinish = strtotime(date("Y-m-$numberOfDaysOfThisMonth 23:59:59"));

        $queryParameters = [
            'timestart' => $dateRangeStart,
            'timeend' => $dateRangeFinish,
            'consider_days'=>true
        ];


        $query = $this->userActivityReport->build_query($queryParameters);
        $sql = "SELECT {$query->fields} FROM {$query->from} WHERE {$query->where}";

        $recordset = $DB->get_recordset_sql($sql,$query->params);

        $records = array();

        foreach ($recordset as $row) {
            array_push($records, $row);
        }

        $recordset->close();

        $this->sendActivityReportToDataAggregattor($records);
    }

    /**
     * @return void
     * @throws \dml_exception
     */
    private function sendActivityReportRetroactive()
    {
        global $DB;


        $actualYear = date('Y');
        $actualMonth = date('m');


        $sql = <<<SQL
        SELECT q.`year`, q.`month`, q.active_users FROM (
            SELECT 
                `year`,`month`,COUNT(*) AS active_users 
            FROM {tool_activeusers_user_actvt}
            WHERE year <= $actualYear
                AND month < $actualMonth
                GROUP BY `year`, `month`
        ) q WHERE 1=1 ORDER BY q.`year`, q.`month` ASC
SQL;

        $recordset = $DB->get_recordset_sql($sql);

        $records = array();

        foreach ($recordset as $row) {
            array_push($records, $row);
        }

        $recordset->close();

        $this->sendActivityReportToDataAggregattor($records);

    }

    /**
     * @param $records
     * @return void
     */
    private function sendActivityReportToDataAggregattor($records)
    {
        global $CFG;
        require_once($CFG->dirroot . '/admin/tool/activeusers/settings.php');

        $userActivityReportApi = get_config('tool_activeusers', 'report_api_url');

        foreach ($records as $record){
            try {
                $url = $userActivityReportApi;
                $curl = curl_init($url);
                curl_setopt($curl, CURLOPT_URL, $url);
                curl_setopt($curl, CURLOPT_POST, true);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_REFERER, $CFG->wwwroot);

                $headers = array(
                    "Content-Type: application/json",
                );

                curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

                $data = json_encode($record);

                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

                //for debug only!
                if($CFG->debug !== null){
                    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                }

                $response = curl_exec($curl);
                curl_close($curl);

                $message = json_decode($response,true)['message'];
                mtrace('Result: '. $message);

            }catch (\Error $e){
                mtrace($e->getMessage());
            }
        }
    }


    /**
     * @return \lang_string|string
     * @throws \coding_exception
     */
    public function get_name()
    {
        return get_string('pluginname','tool_activeusers');
    }
}