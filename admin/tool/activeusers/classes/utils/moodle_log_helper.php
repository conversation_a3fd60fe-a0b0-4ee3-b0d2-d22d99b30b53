<?php namespace tool_activeusers\utils;


abstract class moodle_log_helper{

    static protected $log_table_config = null;

    /**
     * Returns this moodle's instalation configuration for logs.
     *
     * The name of the log table
     * The name of the field that refer to the course id
     * The name of the field that refer to the time
     * @return array
     */
    protected static function get_log_table_config(){

        if(self::$log_table_config !== null){
            return self::$log_table_config;
        }

        $logmanager = get_log_manager();
        $readers = $logmanager->get_readers();
        $enabledreaders = get_config('tool_log', 'enabled_stores');
        if (empty($enabledreaders)) {
            return 0;
        }
        $enabledreaders = explode(',', $enabledreaders);

        // Go through all the readers until we find one that we can use.
        foreach ($enabledreaders as $enabledreader) {
            $reader = $readers[$enabledreader];
            if ($reader instanceof \logstore_legacy\log\store) {
                $logtable = 'log';
                $coursefield = 'course';
                $timefield = 'time';
                break;
            } else if ($reader instanceof \core\log\sql_internal_table_reader) {
                $logtable = $reader->get_internal_log_table_name();
                $coursefield = 'courseid';
                $timefield = 'timecreated';
                break;
            }
        }

        self::$log_table_config =   [
                                        'logtable' => $logtable,
                                        'coursefield' => $coursefield,
                                        'timefield' => $timefield,
                                    ];

        return self::$log_table_config;       
    }
}