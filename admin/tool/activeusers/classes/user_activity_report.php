<?php namespace tool_activeusers;

use \tool_activeusers\utils\moodle_log_helper;
use \tool_activeusers\observers\user_activity_observer;

class user_activity_report{
    
    public function build_query(array $parameters){

        $query = new \StdClass();

        // $query->fields = "`year`,`month`,COUNT(*) AS active_users";

        // $query->from = "mdl_tool_activeusers_user_actvt";
        
        // $query->where ="timecreated >= :timestart
        //                     AND timecreated <= :timeend
        //                 GROUP BY `year`, `month`";

        $query->fields = "q.`year`, q.`month`, q.active_users";

        $query->from = "(
            SELECT 
                `year`,`month`,COUNT(*) AS active_users
            FROM {tool_activeusers_user_actvt}
            WHERE timecreated >= :timestart
                AND timecreated <= :timeend
                GROUP BY `year`, `month`
        ) q";

        $query->where = "1=1 ORDER BY q.`year`, q.`month` ASC";

        // PARAMS

        $timestart = empty($parameters['timestart']) ? 0 : $parameters['timestart'];
        $timeend = empty($parameters['timeend']) ? time() : $parameters['timeend'];

        if(empty($parameters['consider_days'])){
            if($timestart > 0){
                
                $timestart = mktime(
                    0,
                    0,
                    0,
                    date("m", $timestart),
                    1,
                    date("Y", $timestart)
                ); // First of this month:
            }

            $timeend = mktime(
                0,
                0,
                0,
                date("m", $timeend) + 1,
                1,
                date("Y", $timeend)
            ) - 1; // Last day of this month

        }else{
            // Considering the day of the dates
            
            $timeend = mktime(
                0,
                0,
                0,
                date("m", $timeend),
                date("d", $timeend)+1,
                date("Y", $timeend)
            ) - 1; // End of this day
        }

        $query->params = [
            'timestart' => $timestart,
            'timeend' => $timeend,
            'consider_days' => isset($parameters['consider_days']) ? $parameters['consider_days'] : null,
        ];

        return $query;
    }


    public static function display_month_name($month){
        $date = DateTime::createFromFormat('!m', $month);
        $date->setTimezone(\core_date::get_user_timezone_object());
        return $date->format('F');
    }


    public function download_as_xlsx(object $query){

        // var_dump($query); exit();

        global $DB, $CFG;

        require_once($CFG->libdir . '/phpspreadsheet/vendor/autoload.php');

        /* Configs */
        $spreadsheet_author = 'SMART LMS';
        $spreadsheet_title = get_string('active_users_report_title', 'tool_activeusers');
        $spreadsheet_name = get_string('active_users_report_title', 'tool_activeusers');
        $spreadsheet_downlaod_name = get_string('active_users_report_download_name', 'tool_activeusers');

        $site_uri = str_replace(['https://', 'http://', '/'], '', $CFG->wwwroot);
        $spreadsheet_downlaod_name .= "_{$site_uri}";

        $start_line = 9;
        $last_line = $start_line; /* Acts like the current line as well */


        $last_column = 'B';        
        $sitename_cell = 'A2';
        $from_cell = 'B4';
        $until_cell = 'B5';
        $date_cell = 'B6';

        $column_map = [
            'periodo'              => 'A',
            'usuarios_ativos'      => 'B',
        ];


        /* Loading XLSX template: */
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load( $CFG->dirroot . '/admin/tool/activeusers/template.xlsx');

        $spreadsheet
        ->getProperties()
        ->setCreator($spreadsheet_author)
		->setLastModifiedBy($spreadsheet_author);


        /* Retrieving Recordset */

        $sql = "SELECT {$query->fields} FROM {$query->from} WHERE {$query->where}";

        $recordset = $DB->get_recordset_sql($sql, $query->params);


        /* Editing the spreadsheet */

        $active_spreadsheet = $spreadsheet->getActiveSheet();

        // Ediding site name:
        $sitename = $DB->get_field('course', 'fullname', ['id' => SITEID]);
        $active_spreadsheet->setCellValue($sitename_cell, $sitename );


        // Editing Header:

        if($query->params['timestart'] == 0){
            //Use instalation date instead:
            $timestart = $DB->get_field('course', 'timecreated', ['id' => SITEID]);
        }else{
            $timestart = $query->params['timestart'];
        }

        if(!empty($query->params['consider_days'])){
            $from_text = date('d/m/Y', $timestart);
            $until_text = date('d/m/Y', $query->params['timeend']);
        }else{
            $from_text = date('m/Y', $timestart);
            $until_text = date('m/Y', $query->params['timeend']);
        }
        $date_text = date('d/m/Y H:m:i', time());

        $active_spreadsheet->setCellValue($from_cell, $from_text);
        $active_spreadsheet->setCellValue($until_cell, $until_text);
        $active_spreadsheet->setCellValue($date_cell, $date_text);


        // Filling lines:
        foreach ($recordset as $row) {
            
            //Period
            $active_spreadsheet->setCellValue($column_map['periodo'].$last_line, "{$row->month}/{$row->year}" );

            //Active users
            $active_spreadsheet->setCellValue($column_map['usuarios_ativos'].$last_line, $row->active_users );

            $last_line++;

        }
        $recordset->close();


        /* Adding borders to content rows */

        $content_row_style = array(
            'borders' => array(
                'allBorders' => array(
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => array('argb' => 'FF000000'),
                ),
            ),
        );

        $last_line--;
        $active_spreadsheet->getStyle("A{$start_line}:{$last_column}{$last_line}")->applyFromArray($content_row_style);



        /* Autosizing on all cells */
        foreach (array_values($column_map) as $column_letter) {
            $active_spreadsheet->getColumnDimension($column_letter)->setAutoSize(true);
        }


        /* Downloady part from here: */


        // Rename worksheet
        $active_spreadsheet->setTitle($spreadsheet_name);

        // Redirect output to a client’s web browser (Xlsx)
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$spreadsheet_downlaod_name.'.xlsx"');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');

        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }



    // /**
    //  * Called during installation.
    //  * 
    //  * It uses the default log to extract
    //  * user activity information.
    //  *
    //  * @return void
    //  */
    // public static function proccess_from_old_logs(){
    //     global $CFG, $DB;

    //     \core_php_time_limit::raise();
    //     raise_memory_limit(MEMORY_UNLIMITED);

    //     // First, we empty the 'tool_activeusers_user_actvt' table;
    //     // $DB->execute("TRUNCATE TABLE {tool_activeusers_user_actvt}");


    //     /* $sql = "SELECT 
    //                 DATE_FORMAT(FROM_UNIXTIME(timecreated), '%m') AS month,
    //                 DATE_FORMAT(FROM_UNIXTIME(timecreated), '%Y') AS `year`,
    //                 IF(realuserid IS NULL, userid, realuserid) AS userid,
    //                 MIN(timecreated) AS timecreated
    //             FROM mdl_logstore_standard_log
    //             WHERE origin = 'web'
    //             AND action IN ('loggedin', 'viewed')
                        
    //             GROUP BY month, `year`, userid"; */ //It gets an warning from MySQL
        
    //     $sql = "SELECT
    //                 inner_query.month,
    //                 inner_query.`year`,
    //                 inner_query.userid,
    //                 MIN(timecreated) AS timecreated
    //             FROM (
    //                 SELECT 
    //                     DATE_FORMAT(FROM_UNIXTIME(timecreated), '%m') AS month,
    //                     DATE_FORMAT(FROM_UNIXTIME(timecreated), '%Y') AS `year`,
    //                     IF(realuserid IS NULL, userid, realuserid) AS userid,
    //                     timecreated
    //                 FROM {logstore_standard_log}
    //                 WHERE origin = 'web'
    //                 AND action IN ('loggedin', 'viewed')
    //             ) inner_query
                        
    //             GROUP BY month, `year`, userid";


    //     $rs = $DB->get_recordset_sql($sql);

    //     $errors = [];

    //     // Processing:
    //     foreach ($rs as $record) {
    //         try {
    //             if(empty($record->userid)){
    //                 continue;
    //             }
    //             echo "\nUSER #{$record->userid} ({$record->month}/{$record->year})";
    //             $DB->insert_record(user_activity_observer::TABLE, $record);
    //         } catch (\Throwable $th) {
    //             echo " = [ERROR]" ;
    //             $errors[] = $record;
    //             continue;
    //         }
    //         echo " = [OK]" ;

    //     }
    //     $rs->close();

    // }
}