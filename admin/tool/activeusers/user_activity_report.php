<?php
// ob_start();
require('../../../config.php');
require_once($CFG->libdir . '/formslib.php');

use \tool_activeusers\user_activity_report;

require_login();

$reporturl = new moodle_url($CFG->wwwroot . "/admin/tool/activeusers/user_activity_report.php?" . http_build_query($_GET));

$context = \context_system::instance();

$PAGE->set_context($context);
$PAGE->set_url('/admin/tool/activeusers');
$PAGE->set_title(get_string('active_users_report_title', 'tool_activeusers'));
$PAGE->set_pagelayout('report');
$PAGE->navbar->add(get_string('active_users_report_title', 'tool_activeusers'));

try {

    $filter = new \tool_activeusers\user_activity_filter(null, null, 'GET');
    $parameters = (array) $filter->get_data();    

    if(empty($parameters['export'])){
        echo $OUTPUT->header();
        $filter->display();
        $PAGE->requires->js_call_amd('tool_activeusers/export', 'init', []);

        echo $OUTPUT->footer();
        
    }else{
        $report = new user_activity_report();
        $sql = $report->build_query($parameters);

        $report->download_as_xlsx($sql);
    }

} catch (\Throwable $th) {
    if(!empty($parameters['export'])){
        echo $OUTPUT->header();
    }

    echo "<pre>";
    print_r($th);
    
    if(!empty($parameters['export'])){
        echo $OUTPUT->footer();
    }

    // throw $th;
}
