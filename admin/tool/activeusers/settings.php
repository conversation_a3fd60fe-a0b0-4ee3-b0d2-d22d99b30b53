<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file settings
 *
 * @package    tool_activeusers
 * @copyright  2024 Revvo
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
*/

defined('MOODLE_INTERNAL') || die;

if ($hassiteconfig) {
 
    $settings = new admin_settingpage('tool_activeusers_settings', get_string('pluginname', 'tool_activeusers'));
    $ADMIN->add('tools', $settings);

    // Campo para habilitar o plugin
    $settings->add(new admin_setting_configcheckbox(
        $name = 'tool_activeusers/enable',
        $visiblename = get_string('enable_plugin', 'tool_activeusers'),
        $description = '',
        $defaultsetting = '1'
    ));

    // URL da API
    $settings->add(new admin_setting_configtext(
        $name = 'tool_activeusers/report_api_url',
        $visiblename = get_string('api_url', 'tool_activeusers'),
        $description = NULL,
        $defaultsetting = "http://127.0.0.1:8000/api/save/activity-report", // TODO: change default value (development)
    ));


}

