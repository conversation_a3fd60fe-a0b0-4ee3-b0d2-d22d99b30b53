# Active Users
## _Relatório de Usuários Ativos_


Este relatório só aparecerá na Administração do Site, se o usuário logado estiver especificado no arquivo config.php. Exemplo abaixo:
```
$CFG->leoadmins = [2,]; // tool_activeusers

```
Para funcionamento da CRON que envia os dados para o sistema de usuários ativos é necessário que a seguinte varivael seja atribuida no arquivo config.php. O valor da varivavél deve conter o endpoint do ambiente que esta sendo usado para coleta de dados. Exemplo abaixo:

```
$CFG->userActivityReportApi = "http://127.0.0.1:8080/api/save/activity-report";

```


### Pós-instalação
Após a instalação, o plugin irá executar uma tarefa adhoc que utilizará os dados do logstore do Moodle para preencher sua tabela de usuários ativos por mês. Esta tarefa pode demorar um pouco até ser executada, a depender da plataforma, do tamanho do cron e da fila de tarefas adhoc.