<?php


define('CLI_SCRIPT', true);

require_once(__DIR__ . '../../../../config.php');
require_once($CFG->libdir . '/clilib.php');


$usage = "Put a one line summary of what the script does here.

Usage:
    # php cli-setup.php --firstrun=<value>
    # php cli-setup.php [--help|-h]

Options:
    -h --help               Print this help.
    --paramname=<value>     If true, it will force the plugin to proccess the Moodle's log data and populate its reports.
";

list($options, $unrecognised) = cli_get_params([
    'help' => false,
    'firstrun' => null,
], [
    'h' => 'help'
]);

if ($unrecognised) {
    $unrecognised = implode(PHP_EOL . '  ', $unrecognised);
    cli_error(get_string('cliunknowoption', 'core_admin', $unrecognised));
}

if ($options['help']) {
    cli_writeln($usage);
    exit(2);
}

$force_tool_activeusers_first_run = false;
if (!empty($options['firstrun'])) {
    $force_tool_activeusers_first_run = true;  
}


core_php_time_limit::raise();
raise_memory_limit(MEMORY_EXTRA);




$adhoc = new \tool_activeusers\tasks\initial_dump_adhoc_task();
$adhoc->execute();