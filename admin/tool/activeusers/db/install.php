<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Code to be executed after the plugin's database scheme has been installed is defined here.
 *
 * @package     tool_activeusers
 * @category    upgrade
 * @copyright   2021 LEOLEARNING <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Custom code to be run on installing the plugin.
 */
function xmldb_tool_activeusers_install() {

    set_config('finishedinstalation', 0, 'tool_activeusers');

    // Using Moodle Logs to populate user_activity records
    // \tool_activeusers\user_activity_report::proccess_from_old_logs();

    $initial_dump_task = new \tool_activeusers\tasks\initial_dump_adhoc_task();
    \core\task\manager::queue_adhoc_task($initial_dump_task);

    return true;
}
