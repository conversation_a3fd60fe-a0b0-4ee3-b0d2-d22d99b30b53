<?php
// This file is part of the customcert module for Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON><PERSON><PERSON>.  If not, see <http://www.gnu.org/licenses/>.

/**
 * <PERSON>les viewing a customcert.
 *
 * @package    mod_customcert
 * @copyright  2013 Mark Nelson <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once( '../../auth/joomdle/auth.php');


$id = required_param('id', PARAM_INT);
$action = optional_param('action', '', PARAM_ALPHA);
$token         = optional_param('token',  '',  PARAM_TEXT);
$username = optional_param('username',   '',   PARAM_TEXT);

$username = strtolower ($username);

$auth = new auth_plugin_joomdle();
$logged = $auth->call_method ("confirmJoomlaSession", $username, $token);

if (!$logged) {
    return;
}

$USER = get_complete_user_data('username', $username);
complete_user_login($USER);

$cm = get_coursemodule_from_id('customcert', $id, 0, false, MUST_EXIST);
$course = $DB->get_record('course', array('id' => $cm->course), '*', MUST_EXIST);

require_login($course->id, true, $cm);
$context = context_module::instance($cm->id);
require_capability('mod/customcert:view', $context);

$customcert = $DB->get_record('customcert', array('id' => $cm->instance), '*', MUST_EXIST);
$template = $DB->get_record('customcert_templates', array('id' => $customcert->templateid), '*', MUST_EXIST);

$context = context_module::instance($cm->id);

// Initialize $PAGE, compute blocks.
$PAGE->set_url('/mod/customcert/view.php', array('id' => $cm->id));
$PAGE->set_context($context);
$PAGE->set_cm($cm);


// Initialise $PAGE.
$pageurl = new moodle_url('/mod/customcert/view.php', array('id' => $cm->id));
\mod_customcert\page_helper::page_setup($pageurl, $context, format_string($customcert->name));

// Check if the user can view the certificate based on time spent in course.
if ($customcert->requiredtime && !has_capability('mod/certificate:manage', $context)) {
    if (\mod_customcert\certificate::get_course_time($course->id) < ($customcert->requiredtime * 60)) {
        $a = new stdClass;
        $a->requiredtime = $customcert->requiredtime;
        notice(get_string('requiredtimenotmet', 'certificate', $a), "$CFG->wwwroot/course/view.php?id=$course->id");
        die;
    }
}

// Check that no action was passed, if so that means we are not outputting to PDF.
if (empty($action)) {
    // Get the current groups mode.
    if ($groupmode = groups_get_activity_groupmode($cm)) {
        groups_get_activity_group($cm, true);
    }

    // Generate the link to the report if there are issues to display.
    $reportlink = '';
    if (has_capability('mod/customcert:manage', $context)) {
        // Get the total number of issues.
        $numissues = \mod_customcert\certificate::get_number_of_issues($customcert->id, $cm, $groupmode);
        $href = new moodle_urL('/mod/customcert/report.php', array('id' => $cm->id));
        $url = html_writer::tag('a', get_string('viewcustomcertissues', 'customcert', $numissues),
            array('href' => $href->out()));
        $reportlink = html_writer::tag('div', $url, array('class' => 'reportlink'));
    }

    // Generate the intro content if it exists.
    $intro = '';
    if (!empty($customcert->intro)) {
        $intro = $OUTPUT->box(format_module_intro('customcert', $customcert, $cm->id), 'generalbox', 'intro');
    }

    // If the current user has been issued a customcert generate HTML to display the details.
    $issuelist = '';
    if ($issues = $DB->get_records('customcert_issues', array('userid' => $USER->id, 'customcertid' => $customcert->id))) {
        $header = $OUTPUT->heading(get_string('summaryofissue', 'customcert'));

        $table = new html_table();
        $table->class = 'generaltable';
        $table->head = array(get_string('issued', 'customcert'));
        $table->align = array('left');
        $table->attributes = array('style' => 'width:20%; margin:auto');

        foreach ($issues as $issue) {
            $row = array();
            $row[] = userdate($issue->timecreated);
            $table->data[$issue->id] = $row;
        }

        $issuelist = $header . html_writer::table($table) . "<br />";
    }

    // Create the button to download the customcert.
    $linkname = get_string('getcustomcert', 'customcert');
    $link = new moodle_url('/mod/customcert/view.php', array('id' => $cm->id, 'action' => 'download'));
    $downloadbutton = new single_button($link, $linkname);
    $downloadbutton->add_action(new popup_action('click', $link, 'customcertpopup', array('height' => 600, 'width' => 800)));
    $downloadbutton = html_writer::tag('div', $OUTPUT->render($downloadbutton), array('style' => 'text-align:center'));

    // Output all the page data.
    echo $OUTPUT->header();
    groups_print_activity_menu($cm, $pageurl);
    echo $reportlink;
    echo $intro;
    echo $issuelist;
    echo $downloadbutton;
    echo $OUTPUT->footer($course);
    exit;
} else { // Output to pdf
    // Create new customcert issue record if one does not already exist.
    if (!$DB->record_exists('customcert_issues', array('userid' => $USER->id, 'customcertid' => $customcert->id))) {
        $customcertissue = new stdClass();
        $customcertissue->customcertid = $customcert->id;
        $customcertissue->userid = $USER->id;
        $customcertissue->code = \mod_customcert\certificate::generate_code();
        $customcertissue->timecreated = time();
        // Insert the record into the database.
        $DB->insert_record('customcert_issues', $customcertissue);
    }
    // Now we want to generate the PDF.
    $template = new \mod_customcert\template($template);
    $template->generate_pdf();
}
