<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Upgrade script for Custom Roles
 *
 * @package    local_customroles
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Upgrade function for local_customroles plugin.
 *
 * @param int $oldversion The old version number.
 * @return bool True if upgrade succeeded, false otherwise.
 */
function xmldb_local_customroles_upgrade($oldversion) {
    global $CFG, $DB;

    $dbman = $DB->get_manager();

    // Versão atual: **********
    if ($oldversion < **********) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Incluir o arquivo install.php para reutilizar a função create_custom_roles
        require_once(__DIR__ . '/install.php');

        // Atualizar as capabilities dos papéis existentes sem recriá-los
        // Esta versão simplifica o arquivo upgrade.php para resolver problemas de deploy
        create_custom_roles();

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, **********, 'local', 'customroles');
    }

    // Nova versão: 2025061800 - Atualização das capacidades do papel "Editor de Cursos"
    if ($oldversion < 2025061800) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades que o Editor de Cursos NÃO deve ter (proibir)
            $capabilities_to_prohibit = array(
                // Participantes - impedir acesso à página de participantes
                'moodle/course:viewparticipants',  // Impedir visualização de participantes
                'moodle/course:enrolreview',       // Impedir revisão de inscrições
                'moodle/user:viewdetails',         // Impedir visualização de detalhes de usuários
                'enrol/manual:enrol',              // Impedir a inscrição manual de usuários
                'enrol/manual:manage',             // Impedir o gerenciamento de inscrições

                // Notas - impedir acesso ao livro de notas
                'moodle/grade:viewall',            // Impedir visualização de todas as notas
                'moodle/grade:view',               // Impedir visualização de notas
                'moodle/grade:manage',             // Impedir gerenciamento de notas
                'gradereport/grader:view',         // Impedir visualização do relatório de notas
                'gradereport/user:view',           // Impedir visualização do relatório de usuário

                // Relatórios - impedir acesso aos relatórios do curso
                'report/log:view',                 // Impedir visualização de logs
                'report/loglive:view',             // Impedir visualização de logs ao vivo
                'report/outline:view',             // Impedir visualização de relatório de atividades
                'report/participation:view',       // Impedir visualização de relatório de participação
                'report/progress:view',            // Impedir visualização de relatório de progresso

                // Banco de questões - impedir acesso ao banco de questões
                'moodle/question:add',             // Impedir adição de questões
                'moodle/question:editmine',        // Impedir edição de questões próprias
                'moodle/question:editall',         // Impedir edição de todas as questões
                'moodle/question:viewmine',        // Impedir visualização de questões próprias
                'moodle/question:viewall',         // Impedir visualização de todas as questões
                'moodle/question:usemine',         // Impedir uso de questões próprias
                'moodle/question:useall',          // Impedir uso de todas as questões
                'moodle/question:movemine',        // Impedir movimentação de questões próprias
                'moodle/question:moveall',         // Impedir movimentação de todas as questões

                // Banco de conteúdo - impedir acesso ao banco de conteúdo
                'moodle/contentbank:access',       // Impedir acesso ao banco de conteúdo
                'moodle/contentbank:upload',       // Impedir upload para o banco de conteúdo
                'moodle/contentbank:useeditor',    // Impedir uso do editor do banco de conteúdo

                // Conclusão do curso - impedir acesso à configuração de conclusão do curso
                'report/completion:view',          // Impedir visualização do relatório de conclusão

                // Emblemas - impedir acesso aos emblemas
                'moodle/badges:viewbadges',        // Impedir visualização de emblemas
                'moodle/badges:configurecriteria', // Impedir configuração de critérios de emblemas
                'moodle/badges:awardbadge',        // Impedir concessão de emblemas

                // Competências - impedir acesso às competências
                'moodle/competency:competencyview',       // Impedir visualização de competências
                'moodle/competency:competencymanage',     // Impedir gerenciamento de competências
                'moodle/competency:coursecompetencyview', // Impedir visualização de competências do curso
                'moodle/competency:coursecompetencymanage', // Impedir gerenciamento de competências do curso

                // Filtros - impedir acesso aos filtros
                'moodle/filter:manage',            // Impedir gerenciamento de filtros

                // Reuso de curso - impedir acesso ao reuso de curso
                'moodle/backup:backupcourse',      // Impedir backup do curso
                'moodle/backup:backuptargetimport', // Impedir backup para importação
                'moodle/restore:restorecourse',    // Impedir restauração do curso
                'moodle/restore:restoretargetimport', // Impedir importação

                // Grupos - impedir gerenciamento de grupos
                'moodle/course:managegroups',      // Impedir gerenciamento de grupos

                // Papéis - impedir atribuição de papéis
                'moodle/role:assign',              // Impedir atribuição de papéis
                'moodle/role:review',              // Impedir revisão de papéis
                'moodle/role:override',            // Impedir sobrescrita de papéis

                // Configurações da página inicial do site - impedir acesso às configurações da página inicial
                'moodle/course:update',            // Impedir atualização do curso (quando aplicado ao contexto da página inicial)
                'moodle/course:changefullname',    // Impedir alteração do nome completo do site
                'moodle/course:changeshortname',   // Impedir alteração do nome curto do site
                'moodle/course:changesummary',     // Impedir alteração do resumo do site

                // Impedir o acesso ao menu "Administração do site"
                'moodle/site:config',              // Impedir configuração do site
                'moodle/site:configview'           // Impedir visualização da configuração do site
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025061800, 'local', 'customroles');
    }

    // Nova versão: 2025061900 - Atualização das capacidades do papel "Editor de Cursos" para restringir acesso à página inicial
    if ($oldversion < 2025061900) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades adicionais que o Editor de Cursos NÃO deve ter (proibir)
            $capabilities_to_prohibit = array(
                // Configurações da página inicial do site - impedir acesso às configurações da página inicial
                'moodle/course:update',            // Impedir atualização do curso (quando aplicado ao contexto da página inicial)
                'moodle/course:changefullname',    // Impedir alteração do nome completo do site
                'moodle/course:changeshortname',   // Impedir alteração do nome curto do site
                'moodle/course:changesummary',     // Impedir alteração do resumo do site
                'moodle/site:configview'           // Impedir visualização da configuração do site
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Contexto da página inicial (SITEID)
            $frontpagecontext = context_course::instance(SITEID);

            // Proibir as capacidades para o papel de Editor de Cursos no contexto da página inicial
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $frontpagecontext->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $frontpagecontext->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025061900, 'local', 'customroles');
    }

    // Nova versão: 2025062000 - Permitir que o papel "Coordenador" atribua os papéis de estudante e professor
    if ($oldversion < 2025062000) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Coordenador
        $coordenador_role = $DB->get_record('role', array('shortname' => 'coordenador'));
        if ($coordenador_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Verificar se o papel de Coordenador tem a capacidade 'moodle/role:assign'
            $params = array(
                'roleid' => $coordenador_role->id,
                'capability' => 'moodle/role:assign',
                'contextid' => $context->id
            );

            $existing = $DB->get_record('role_capabilities', $params);

            if ($existing) {
                if ($existing->permission != CAP_ALLOW) {
                    $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                }
            } else {
                assign_capability('moodle/role:assign', CAP_ALLOW, $coordenador_role->id, $context->id, true);
            }

            // Permitir que o papel de Coordenador atribua os papéis de estudante, professor e professor editor
            $student_role = $DB->get_record('role', array('shortname' => 'student'));
            if ($student_role) {
                if (!$DB->record_exists('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $student_role->id))) {
                    core_role_set_assign_allowed($coordenador_role->id, $student_role->id);
                }
            }

            $teacher_role = $DB->get_record('role', array('shortname' => 'teacher'));
            if ($teacher_role) {
                if (!$DB->record_exists('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $teacher_role->id))) {
                    core_role_set_assign_allowed($coordenador_role->id, $teacher_role->id);
                }
            }

            $editingteacher_role = $DB->get_record('role', array('shortname' => 'editingteacher'));
            if ($editingteacher_role) {
                if (!$DB->record_exists('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $editingteacher_role->id))) {
                    core_role_set_assign_allowed($coordenador_role->id, $editingteacher_role->id);
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062000, 'local', 'customroles');
    }

    // Nova versão: 2025062100 - Ajustar permissões do papel "Coordenador" para atribuir apenas os papéis de estudante e professor (editingteacher)
    if ($oldversion < 2025062100) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Coordenador
        $coordenador_role = $DB->get_record('role', array('shortname' => 'coordenador'));
        if ($coordenador_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Verificar se o papel de Coordenador tem a capacidade 'moodle/role:assign'
            $params = array(
                'roleid' => $coordenador_role->id,
                'capability' => 'moodle/role:assign',
                'contextid' => $context->id
            );

            $existing = $DB->get_record('role_capabilities', $params);

            if ($existing) {
                if ($existing->permission != CAP_ALLOW) {
                    $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                }
            } else {
                assign_capability('moodle/role:assign', CAP_ALLOW, $coordenador_role->id, $context->id, true);
            }

            // Permitir que o papel de Coordenador atribua apenas os papéis de estudante e professor (editingteacher)
            $student_role = $DB->get_record('role', array('shortname' => 'student'));
            if ($student_role) {
                if (!$DB->record_exists('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $student_role->id))) {
                    core_role_set_assign_allowed($coordenador_role->id, $student_role->id);
                }
            }

            $editingteacher_role = $DB->get_record('role', array('shortname' => 'editingteacher'));
            if ($editingteacher_role) {
                if (!$DB->record_exists('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $editingteacher_role->id))) {
                    core_role_set_assign_allowed($coordenador_role->id, $editingteacher_role->id);
                }
            }

            // Remover permissão para atribuir o papel de professor não editor (teacher), se existir
            $teacher_role = $DB->get_record('role', array('shortname' => 'teacher'));
            if ($teacher_role) {
                if ($DB->record_exists('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $teacher_role->id))) {
                    $DB->delete_records('role_allow_assign', array('roleid' => $coordenador_role->id, 'allowassign' => $teacher_role->id));
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062100, 'local', 'customroles');
    }

    // Nova versão: 2025062200 - Ajustar permissões do papel "Editor de Cursos" para permitir visualização de participantes e criação de grupos
    if ($oldversion < 2025062200) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades que o Editor de Cursos DEVE ter (permitir)
            $capabilities_to_allow = array(
                // Participantes - permitir acesso à página de participantes
                'moodle/course:viewparticipants',  // Permitir visualização de participantes
                'moodle/course:enrolreview',       // Permitir revisão de inscrições
                'moodle/user:viewdetails',         // Permitir visualização de detalhes de usuários
                'moodle/course:managegroups'       // Permitir gerenciamento de grupos (criação, edição, exclusão)
            );

            // Capacidades que o Editor de Cursos NÃO deve ter (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:enrol',              // Impedir a inscrição manual de usuários
                'enrol/manual:manage'              // Impedir o gerenciamento de inscrições
            );

            // Permitir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_allow as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062200, 'local', 'customroles');
    }

    // Nova versão: 2025062300 - Ajustar permissões do papel "Editor de Cursos" para visualização apenas
    if ($oldversion < 2025062300) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades que o Editor de Cursos DEVE ter (permitir)
            $capabilities_to_allow = array(
                // Participantes - permitir acesso à página de participantes
                'moodle/course:viewparticipants',  // Permitir visualização de participantes
                'moodle/course:enrolreview',       // Permitir revisão de inscrições
                'moodle/user:viewdetails'          // Permitir visualização de detalhes de usuários
            );

            // Capacidades que o Editor de Cursos NÃO deve ter (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:enrol',              // Impedir a inscrição manual de usuários
                'enrol/manual:manage'              // Impedir o gerenciamento de inscrições
            );

            // Permitir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_allow as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062300, 'local', 'customroles');
    }

    // Nova versão: 2025062400 - Ajustar permissões do papel "Editor de Cursos" para impedir desinscrição de usuários
    if ($oldversion < 2025062400) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades que o Editor de Cursos NÃO deve ter (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:unenrol'            // Impedir a desinscrição manual de usuários
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062400, 'local', 'customroles');
    }

    // Nova versão: 2025062500 - Remover a capacidade de gerenciar grupos do papel "Editor de Cursos"
    if ($oldversion < 2025062500) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidade que o Editor de Cursos NÃO deve ter (proibir)
            $capability = 'moodle/course:managegroups';

            // Proibir a capacidade para o papel de Editor de Cursos
            if ($DB->record_exists('capabilities', array('name' => $capability))) {
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $capability,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_PROHIBIT) {
                        $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062500, 'local', 'customroles');
    }

    // Nova versão: 2025062600 - Ajustar permissões do papel "Editor de Cursos" para permitir apenas a criação de métodos de inscrição
    if ($oldversion < 2025062600) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidade que o Editor de Cursos DEVE ter (permitir)
            $capability_to_allow = 'moodle/course:enrolconfig';

            // Permitir a capacidade para o papel de Editor de Cursos
            if ($DB->record_exists('capabilities', array('name' => $capability_to_allow))) {
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $capability_to_allow,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_ALLOW) {
                        $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($capability_to_allow, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                }
            }

            // Capacidades que o Editor de Cursos NÃO deve ter (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:manage',
                'enrol/self:manage'
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062600, 'local', 'customroles');
    }

    // Nova versão: 2025062700 - Ajustar permissões do papel "Editor de Cursos" para permitir a visualização do select de métodos de inscrição
    if ($oldversion < 2025062700) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidade que o Editor de Cursos DEVE ter (permitir)
            $capability_to_allow = 'moodle/course:enrolconfig';

            // Permitir a capacidade para o papel de Editor de Cursos
            if ($DB->record_exists('capabilities', array('name' => $capability_to_allow))) {
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $capability_to_allow,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_ALLOW) {
                        $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($capability_to_allow, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                }
            }

            // Capacidades específicas para adicionar métodos de inscrição (permitir)
            $capabilities_to_allow = array(
                'enrol/manual:config',    // Permitir adicionar método de inscrição manual
                'enrol/self:config',      // Permitir adicionar método de auto-inscrição
                'enrol/guest:config',     // Permitir adicionar método de inscrição de convidados
                'enrol/cohort:config',    // Permitir adicionar método de inscrição por coorte
                'enrol/meta:config'       // Permitir adicionar método de inscrição por meta-curso
            );

            // Permitir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_allow as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Capacidades específicas para gerenciar métodos de inscrição (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:manage',    // Proibir gerenciar inscrições manuais
                'enrol/self:manage',      // Proibir gerenciar auto-inscrições
                'enrol/cohort:manage',    // Proibir gerenciar inscrições por coorte
                'enrol/meta:manage'       // Proibir gerenciar inscrições por meta-curso
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062700, 'local', 'customroles');
    }

    // Nova versão: 2025062800 - Ajustar permissões do papel "Editor de Cursos" para permitir a visualização do select de métodos de inscrição, mas impedir a edição e exclusão
    if ($oldversion < 2025062800) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidade que o Editor de Cursos DEVE ter (permitir)
            $capability_to_allow = 'moodle/course:enrolconfig';

            // Permitir a capacidade para o papel de Editor de Cursos
            if ($DB->record_exists('capabilities', array('name' => $capability_to_allow))) {
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $capability_to_allow,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_ALLOW) {
                        $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($capability_to_allow, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                }
            }

            // Capacidades específicas para configurar métodos de inscrição (permitir)
            $capabilities_to_allow_config = array(
                'enrol/manual:config',    // Permitir configuração do método de inscrição manual
                'enrol/self:config',      // Permitir configuração do método de auto-inscrição
                'enrol/guest:config',     // Permitir configuração do método de inscrição de convidados
                'enrol/cohort:config',    // Permitir configuração do método de inscrição por coorte
                'enrol/meta:config'       // Permitir configuração do método de inscrição por meta-curso
            );

            // Permitir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_allow_config as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Capacidades específicas para gerenciar inscrições de usuários (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:enrol',     // Proibir inscrever usuários manualmente
                'enrol/manual:unenrol'    // Proibir desinscrever usuários manualmente
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062800, 'local', 'customroles');
    }

    // Nova versão: 2025062900 - Ajustar permissões do papel "Editor de Cursos" para permitir a criação, edição e exclusão de métodos de inscrição
    if ($oldversion < 2025062900) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidade que o Editor de Cursos DEVE ter (permitir)
            $capability_to_allow = 'moodle/course:enrolconfig';

            // Permitir a capacidade para o papel de Editor de Cursos
            if ($DB->record_exists('capabilities', array('name' => $capability_to_allow))) {
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $capability_to_allow,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_ALLOW) {
                        $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($capability_to_allow, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                }
            }

            // Capacidades específicas para configurar métodos de inscrição (permitir)
            $capabilities_to_allow = array(
                'enrol/manual:config',    // Permitir configuração do método de inscrição manual
                'enrol/self:config',      // Permitir configuração do método de auto-inscrição
                'enrol/guest:config',     // Permitir configuração do método de inscrição de convidados
                'enrol/cohort:config',    // Permitir configuração do método de inscrição por coorte
                'enrol/meta:config'       // Permitir configuração do método de inscrição por meta-curso
            );

            // Permitir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_allow as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Capacidades específicas para gerenciar inscrições de usuários (proibir)
            $capabilities_to_prohibit = array(
                'enrol/manual:enrol',     // Proibir inscrever usuários manualmente
                'enrol/manual:unenrol'    // Proibir desinscrever usuários manualmente
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025062900, 'local', 'customroles');
    }

    // Nova versão: 2025063000 - Ajustar permissões do papel "Editor de Cursos" para impedir a edição de inscrições do Gerenciador de Ofertas
    if ($oldversion < 2025063000) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades específicas do Gerenciador de Ofertas (proibir)
            $capabilities_to_prohibit = array(
                'local/offermanager:manage',
                'local/offermanager:manageparticipants',
                'enrol/offer_manual:manage',
                'enrol/offer_manual:enrol',
                'enrol/offer_manual:unenrol',
                'enrol/offer_self:manage',
                'enrol/offer_self:enrol',
                'enrol/offer_self:unenrol'
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063000, 'local', 'customroles');
    }

    // Nova versão: 2025063100 - Ajustar permissões do papel "Editor de Cursos" para impedir a criação de categorias e o gerenciamento de áreas de interesse
    if ($oldversion < 2025063100) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidades específicas para gerenciar categorias e áreas de interesse (proibir)
            $capabilities_to_prohibit = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage'         // Impedir o gerenciamento de áreas de interesse
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063100, 'local', 'customroles');
    }

    // Nova versão: 2025063200 - Ajustar permissões do papel "Editor de Cursos" para impedir a exibição dos botões na página de Gerenciamento de cursos
    if ($oldversion < 2025063200) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // 1. Proibir capacidades específicas para gerenciar categorias e áreas de interesse
            $capabilities_to_prohibit = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage',        // Impedir o gerenciamento de áreas de interesse
                'moodle/course:changecategory',     // Impedir a mudança de categoria de cursos
                'tool/coursemanagement:edit'        // Impedir a edição no gerenciamento de cursos
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // 2. Permitir apenas a capacidade 'tool/coursemanagement:view'
            $view_capability = 'tool/coursemanagement:view';
            if ($DB->record_exists('capabilities', array('name' => $view_capability))) {
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $view_capability,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_ALLOW) {
                        $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($view_capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063200, 'local', 'customroles');
    }

    // Nova versão: 2025063300 - Ajustar permissões do papel "Editor de Cursos" para permitir a criação de cursos, mas impedir a criação de categorias
    if ($oldversion < 2025063300) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // 1. Proibir capacidades específicas para gerenciar categorias
            $capabilities_to_prohibit = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage'         // Impedir o gerenciamento de áreas de interesse
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_prohibit as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // 2. Permitir capacidades específicas para criar cursos
            $capabilities_to_allow = array(
                'moodle/course:create',             // Permitir a criação de cursos
                'moodle/course:update',             // Permitir a atualização de cursos
                'moodle/course:view',               // Permitir a visualização de cursos
                'moodle/course:changefullname',     // Permitir a alteração do nome completo do curso
                'moodle/course:changeshortname',    // Permitir a alteração do nome curto do curso
                'moodle/course:changeidnumber',     // Permitir a alteração do número de ID do curso
                'moodle/course:changesummary',      // Permitir a alteração do resumo do curso
                'tool/coursemanagement:view'        // Permitir a visualização do gerenciamento de cursos
            );

            // Permitir as capacidades para o papel de Editor de Cursos
            foreach ($capabilities_to_allow as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063300, 'local', 'customroles');
    }

    // Nova versão: 2025063400 - Corrigir permissões do papel "Editor de Cursos" para garantir que ele possa criar e editar cursos
    if ($oldversion < 2025063400) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // 1. Capacidades essenciais para criar e editar cursos (permitir)
            $essential_capabilities = array(
                'moodle/course:create',             // Permitir a criação de cursos
                'moodle/course:update',             // Permitir a atualização de cursos
                'moodle/course:view',               // Permitir a visualização de cursos
                'moodle/course:viewhiddencourses',  // Permitir a visualização de cursos ocultos
                'moodle/course:visibility',         // Permitir alterar a visibilidade de cursos
                'moodle/course:managefiles',        // Permitir gerenciar arquivos de cursos
                'moodle/course:changefullname',     // Permitir a alteração do nome completo do curso
                'moodle/course:changeshortname',    // Permitir a alteração do nome curto do curso
                'moodle/course:changeidnumber',     // Permitir a alteração do número de ID do curso
                'moodle/course:changesummary',      // Permitir a alteração do resumo do curso
                'moodle/course:configurecustomfields', // Permitir configurar campos personalizados
                'moodle/course:activityvisibility', // Permitir alterar a visibilidade de atividades
                'moodle/course:viewhiddenactivities', // Permitir visualizar atividades ocultas
                'moodle/course:manageactivities',   // Permitir gerenciar atividades
                'moodle/course:enrolconfig',        // Permitir configurar métodos de inscrição
                'moodle/course:changecategory',     // Permitir selecionar a categoria ao criar um curso
                'tool/coursemanagement:view'        // Permitir a visualização do gerenciamento de cursos
            );

            // Permitir as capacidades essenciais para o papel de Editor de Cursos
            foreach ($essential_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // 2. Capacidades que devem ser proibidas
            $prohibited_capabilities = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage',        // Impedir o gerenciamento de áreas de interesse
                'tool/coursemanagement:edit'        // Impedir a edição no gerenciamento de cursos
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($prohibited_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063400, 'local', 'customroles');
    }

    // Nova versão: 2025063500 - Corrigir permissões do papel "Editor de Cursos" para garantir que ele possa criar e editar cursos, mas não possa adicionar novas categorias
    if ($oldversion < 2025063500) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // 1. Capacidades essenciais para criar e editar cursos (permitir)
            $essential_capabilities = array(
                'moodle/course:create',             // Permitir a criação de cursos
                'moodle/course:update',             // Permitir a atualização de cursos
                'moodle/course:view',               // Permitir a visualização de cursos
                'moodle/course:viewhiddencourses',  // Permitir a visualização de cursos ocultos
                'moodle/course:visibility',         // Permitir alterar a visibilidade de cursos
                'moodle/course:managefiles',        // Permitir gerenciar arquivos de cursos
                'moodle/course:changefullname',     // Permitir a alteração do nome completo do curso
                'moodle/course:changeshortname',    // Permitir a alteração do nome curto do curso
                'moodle/course:changeidnumber',     // Permitir a alteração do número de ID do curso
                'moodle/course:changesummary',      // Permitir a alteração do resumo do curso
                'moodle/course:changecategory',     // Permitir selecionar a categoria ao criar um curso
                'moodle/course:configurecustomfields', // Permitir configurar campos personalizados
                'moodle/course:activityvisibility', // Permitir alterar a visibilidade de atividades
                'moodle/course:viewhiddenactivities', // Permitir visualizar atividades ocultas
                'moodle/course:manageactivities',   // Permitir gerenciar atividades
                'moodle/course:enrolconfig',        // Permitir configurar métodos de inscrição
                'tool/coursemanagement:view'        // Permitir a visualização do gerenciamento de cursos
            );

            // Permitir as capacidades essenciais para o papel de Editor de Cursos
            foreach ($essential_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // 2. Capacidades que devem ser proibidas
            $prohibited_capabilities = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage'         // Impedir o gerenciamento de áreas de interesse
            );

            // Proibir as capacidades para o papel de Editor de Cursos
            foreach ($prohibited_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063500, 'local', 'customroles');
    }

    // Nova versão: 2025063600 - Corrigir permissões do papel "Editor de Cursos" para garantir que ele possa criar e editar cursos em todas as categorias
    if ($oldversion < 2025063600) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // 1. Capacidades essenciais para criar e editar cursos (permitir)
            $essential_capabilities = array(
                'moodle/course:create',             // Permitir a criação de cursos
                'moodle/course:update',             // Permitir a atualização de cursos
                'moodle/course:view',               // Permitir a visualização de cursos
                'moodle/course:viewhiddencourses',  // Permitir a visualização de cursos ocultos
                'moodle/course:visibility',         // Permitir alterar a visibilidade de cursos
                'moodle/course:managefiles',        // Permitir gerenciar arquivos de cursos
                'moodle/course:changefullname',     // Permitir a alteração do nome completo do curso
                'moodle/course:changeshortname',    // Permitir a alteração do nome curto do curso
                'moodle/course:changeidnumber',     // Permitir a alteração do número de ID do curso
                'moodle/course:changesummary',      // Permitir a alteração do resumo do curso
                'moodle/course:changecategory',     // Permitir selecionar a categoria ao criar um curso
                'moodle/course:configurecustomfields', // Permitir configurar campos personalizados
                'moodle/course:activityvisibility', // Permitir alterar a visibilidade de atividades
                'moodle/course:viewhiddenactivities', // Permitir visualizar atividades ocultas
                'moodle/course:manageactivities',   // Permitir gerenciar atividades
                'moodle/course:enrolconfig',        // Permitir configurar métodos de inscrição
                'moodle/course:movesections',       // Permitir mover seções
                'tool/coursemanagement:view'        // Permitir a visualização do gerenciamento de cursos
            );

            // 2. Capacidades que devem ser proibidas
            $prohibited_capabilities = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage'         // Impedir o gerenciamento de áreas de interesse
            );

            // 3. Obter todas as categorias de cursos
            $categories = $DB->get_records('course_categories');

            // 4. Para cada categoria, atribuir as capacidades necessárias
            foreach ($categories as $category) {
                $categorycontext = context_coursecat::instance($category->id);

                // Permitir capacidades essenciais para cada categoria
                foreach ($essential_capabilities as $capability) {
                    if ($DB->record_exists('capabilities', array('name' => $capability))) {
                        $params = array(
                            'roleid' => $editorcursos_role->id,
                            'capability' => $capability,
                            'contextid' => $categorycontext->id
                        );

                        $existing = $DB->get_record('role_capabilities', $params);

                        if ($existing) {
                            if ($existing->permission != CAP_ALLOW) {
                                $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                            }
                        } else {
                            assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $categorycontext->id, true);
                        }
                    }
                }

                // Proibir capacidades específicas para cada categoria
                foreach ($prohibited_capabilities as $capability) {
                    if ($DB->record_exists('capabilities', array('name' => $capability))) {
                        $params = array(
                            'roleid' => $editorcursos_role->id,
                            'capability' => $capability,
                            'contextid' => $categorycontext->id
                        );

                        $existing = $DB->get_record('role_capabilities', $params);

                        if ($existing) {
                            if ($existing->permission != CAP_PROHIBIT) {
                                $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                            }
                        } else {
                            assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $categorycontext->id, true);
                        }
                    }
                }
            }

            // 5. Também atribuir as capacidades no contexto do sistema
            // Permitir capacidades essenciais no contexto do sistema
            foreach ($essential_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Proibir capacidades específicas no contexto do sistema
            foreach ($prohibited_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063600, 'local', 'customroles');
    }

    // Nova versão: 2025063700 - Liberar completamente as permissões para criar e editar cursos para o papel "Editor de Cursos"
    if ($oldversion < 2025063700) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Editor de Cursos
        $editorcursos_role = $DB->get_record('role', array('shortname' => 'editorcursos'));
        if ($editorcursos_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // 1. Capacidades essenciais para criar e editar cursos (permitir)
            $essential_capabilities = array(
                'moodle/course:create',             // Permitir a criação de cursos
                'moodle/course:update',             // Permitir a atualização de cursos
                'moodle/course:view',               // Permitir a visualização de cursos
                'moodle/course:viewhiddencourses',  // Permitir a visualização de cursos ocultos
                'moodle/course:visibility',         // Permitir alterar a visibilidade de cursos
                'moodle/course:managefiles',        // Permitir gerenciar arquivos de cursos
                'moodle/course:changefullname',     // Permitir a alteração do nome completo do curso
                'moodle/course:changeshortname',    // Permitir a alteração do nome curto do curso
                'moodle/course:changeidnumber',     // Permitir a alteração do número de ID do curso
                'moodle/course:changesummary',      // Permitir a alteração do resumo do curso
                'moodle/course:changecategory',     // Permitir selecionar a categoria ao criar um curso
                'moodle/course:configurecustomfields', // Permitir configurar campos personalizados
                'moodle/course:activityvisibility', // Permitir alterar a visibilidade de atividades
                'moodle/course:viewhiddenactivities', // Permitir visualizar atividades ocultas
                'moodle/course:manageactivities',   // Permitir gerenciar atividades
                'moodle/course:enrolconfig',        // Permitir configurar métodos de inscrição
                'moodle/course:movesections',       // Permitir mover seções
                'moodle/course:tag',                // Permitir adicionar tags aos cursos
                'tool/coursemanagement:view',       // Permitir a visualização do gerenciamento de cursos
                'tool/coursemanagement:edit'        // Permitir a edição no gerenciamento de cursos
            );

            // 2. Capacidades que devem ser proibidas
            $prohibited_capabilities = array(
                'moodle/category:manage',           // Impedir a criação e gerenciamento de categorias
                'tool/interestareas:manage'         // Impedir o gerenciamento de áreas de interesse
            );

            // 3. Obter todas as categorias de cursos
            $categories = $DB->get_records('course_categories');

            // 4. Para cada categoria, atribuir as capacidades necessárias
            foreach ($categories as $category) {
                $categorycontext = context_coursecat::instance($category->id);

                // Permitir capacidades essenciais para cada categoria
                foreach ($essential_capabilities as $capability) {
                    if ($DB->record_exists('capabilities', array('name' => $capability))) {
                        $params = array(
                            'roleid' => $editorcursos_role->id,
                            'capability' => $capability,
                            'contextid' => $categorycontext->id
                        );

                        $existing = $DB->get_record('role_capabilities', $params);

                        if ($existing) {
                            if ($existing->permission != CAP_ALLOW) {
                                $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                            }
                        } else {
                            assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $categorycontext->id, true);
                        }
                    }
                }

                // Proibir capacidades específicas para cada categoria
                foreach ($prohibited_capabilities as $capability) {
                    if ($DB->record_exists('capabilities', array('name' => $capability))) {
                        $params = array(
                            'roleid' => $editorcursos_role->id,
                            'capability' => $capability,
                            'contextid' => $categorycontext->id
                        );

                        $existing = $DB->get_record('role_capabilities', $params);

                        if ($existing) {
                            if ($existing->permission != CAP_PROHIBIT) {
                                $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                            }
                        } else {
                            assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $categorycontext->id, true);
                        }
                    }
                }
            }

            // 5. Também atribuir as capacidades no contexto do sistema
            // Permitir capacidades essenciais no contexto do sistema
            foreach ($essential_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_ALLOW) {
                            $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_ALLOW, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // Proibir capacidades específicas no contexto do sistema
            foreach ($prohibited_capabilities as $capability) {
                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $editorcursos_role->id,
                        'capability' => $capability,
                        'contextid' => $context->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $editorcursos_role->id, $context->id, true);
                    }
                }
            }

            // 6. Remover quaisquer proibições existentes para capacidades de criação e edição de cursos
            $capabilities_to_check = array(
                'moodle/course:create',
                'moodle/course:update',
                'moodle/course:changecategory'
            );

            foreach ($capabilities_to_check as $capability) {
                // Verificar no contexto do sistema
                $params = array(
                    'roleid' => $editorcursos_role->id,
                    'capability' => $capability,
                    'permission' => CAP_PROHIBIT
                );

                $records = $DB->get_records('role_capabilities', $params);
                foreach ($records as $record) {
                    $DB->set_field('role_capabilities', 'permission', CAP_ALLOW, array('id' => $record->id));
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063700, 'local', 'customroles');
    }

    // Nova versão: 2025063800 - Remover a permissão "Gerenciar áreas de interesse" do papel "Coordenador"
    if ($oldversion < 2025063800) {
        // Carregar a biblioteca de acesso
        require_once($CFG->libdir . '/accesslib.php');

        // Obter o papel de Coordenador
        $coordenador_role = $DB->get_record('role', array('shortname' => 'coordenador'));
        if ($coordenador_role) {
            // Contexto do sistema
            $context = context_system::instance();

            // Capacidade a ser proibida
            $capability = 'tool/interestareas:manage';

            // 1. Proibir a capacidade no contexto do sistema
            if ($DB->record_exists('capabilities', array('name' => $capability))) {
                $params = array(
                    'roleid' => $coordenador_role->id,
                    'capability' => $capability,
                    'contextid' => $context->id
                );

                $existing = $DB->get_record('role_capabilities', $params);

                if ($existing) {
                    if ($existing->permission != CAP_PROHIBIT) {
                        $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                    }
                } else {
                    assign_capability($capability, CAP_PROHIBIT, $coordenador_role->id, $context->id, true);
                }
            }

            // 2. Obter todas as categorias de cursos
            $categories = $DB->get_records('course_categories');

            // 3. Para cada categoria, proibir a capacidade
            foreach ($categories as $category) {
                $categorycontext = context_coursecat::instance($category->id);

                if ($DB->record_exists('capabilities', array('name' => $capability))) {
                    $params = array(
                        'roleid' => $coordenador_role->id,
                        'capability' => $capability,
                        'contextid' => $categorycontext->id
                    );

                    $existing = $DB->get_record('role_capabilities', $params);

                    if ($existing) {
                        if ($existing->permission != CAP_PROHIBIT) {
                            $DB->set_field('role_capabilities', 'permission', CAP_PROHIBIT, array('id' => $existing->id));
                        }
                    } else {
                        assign_capability($capability, CAP_PROHIBIT, $coordenador_role->id, $categorycontext->id, true);
                    }
                }
            }
        }

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063800, 'local', 'customroles');
    }

    // Nova versão: 2025063900 - Instalação automática do plugin CustomCert
    if ($oldversion < 2025063900) {
        // Função para instalar automaticamente o CustomCert
        auto_install_customcert_plugin();

        // Atualizar a versão do plugin
        upgrade_plugin_savepoint(true, 2025063900, 'local', 'customroles');
    }

    return true;
}

/**
 * Função para instalar automaticamente o plugin CustomCert
 * Esta função é executada durante o upgrade para garantir que o CustomCert
 * seja instalado automaticamente no servidor de QA sem modificar a pipeline
 */
function auto_install_customcert_plugin() {
    global $CFG, $DB;

    // Verificar se o plugin já está instalado
    $plugin_dir = $CFG->dirroot . '/mod/customcert';

    if (!is_dir($plugin_dir)) {
        // Plugin não está presente, baixar e instalar
        try {
            // Criar diretório temporário
            $temp_dir = sys_get_temp_dir() . '/customcert_auto_install_' . uniqid();
            mkdir($temp_dir, 0755, true);

            // URL do plugin compatível com Moodle 4.2
            $plugin_url = "https://github.com/mdjnelson/moodle-mod_customcert/archive/refs/heads/MOODLE_402_STABLE.zip";

            // Baixar usando curl
            $zip_file = $temp_dir . '/customcert.zip';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $plugin_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Moodle CustomCert Auto Installer');
            curl_setopt($ch, CURLOPT_TIMEOUT, 300);

            $data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code === 200 && $data !== false) {
                file_put_contents($zip_file, $data);

                // Extrair
                $zip = new ZipArchive();
                if ($zip->open($zip_file) === TRUE) {
                    $extract_dir = $temp_dir . '/extract';
                    mkdir($extract_dir, 0755, true);

                    $zip->extractTo($extract_dir);
                    $zip->close();

                    // Encontrar o diretório extraído
                    $extracted_dirs = glob($extract_dir . '/*', GLOB_ONLYDIR);
                    if (!empty($extracted_dirs)) {
                        $source_dir = $extracted_dirs[0];

                        // Mover para o local final
                        if (rename($source_dir, $plugin_dir)) {
                            // Plugin instalado com sucesso
                        }
                    }
                }
            }

            // Limpar arquivos temporários
            if (is_dir($temp_dir)) {
                exec("rm -rf " . escapeshellarg($temp_dir));
            }

        } catch (Exception $e) {
            // Em caso de erro, continuar silenciosamente
            // O plugin pode ser instalado manualmente se necessário
        }
    }

    // Verificar se as tabelas já existem e registrar o plugin se necessário
    $dbman = $DB->get_manager();
    $tables_to_check = [
        'customcert',
        'customcert_templates',
        'customcert_issues',
        'customcert_pages',
        'customcert_elements'
    ];

    $existing_tables = [];
    foreach ($tables_to_check as $table) {
        if ($dbman->table_exists($table)) {
            $existing_tables[] = $table;
        }
    }

    // Se todas as tabelas existem, registrar o plugin e proteger contra reinstalação
    if (count($existing_tables) === count($tables_to_check)) {
        // Verificar se o plugin está registrado
        $plugin_version = $DB->get_field('config_plugins', 'value',
            ['plugin' => 'mod_customcert', 'name' => 'version']);

        if (!$plugin_version) {
            // Registrar o plugin
            $versionrecord = new stdClass();
            $versionrecord->plugin = 'mod_customcert';
            $versionrecord->name = 'version';
            $versionrecord->value = 2023042419;
            $DB->insert_record('config_plugins', $versionrecord);
        }

        // Verificar se o módulo está registrado
        $module_exists = $DB->record_exists('modules', ['name' => 'customcert']);
        if (!$module_exists) {
            $module = new stdClass();
            $module->name = 'customcert';
            $module->version = 2023042419;
            $module->cron = 0;
            $module->lastcron = 0;
            $module->search = '';
            $module->visible = 1;

            $DB->insert_record('modules', $module);
        }

        // PROTEÇÃO CRÍTICA: Modificar install.xml para evitar erro de tabelas existentes
        $install_xml_path = $CFG->dirroot . '/mod/customcert/db/install.xml';
        $backup_xml_path = $CFG->dirroot . '/mod/customcert/db/install.xml.backup';

        if (file_exists($install_xml_path) && !file_exists($backup_xml_path)) {
            // Fazer backup do install.xml original
            copy($install_xml_path, $backup_xml_path);

            // Criar install.xml com tabela dummy para evitar criação de tabelas reais
            $minimal_xml = '<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="mod/customcert/db" VERSION="20240313" COMMENT="XMLDB file for Moodle mod/customcert - TABLES ALREADY EXIST"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd">
  <TABLES>
    <TABLE NAME="customcert_dummy" COMMENT="Dummy table to satisfy XMLDB requirements - tables already exist">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="dummy" TYPE="char" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>';

            file_put_contents($install_xml_path, $minimal_xml);
        }

        // Limpar tabela dummy se ela existir
        if ($dbman->table_exists('customcert_dummy')) {
            $dummy_table = new xmldb_table('customcert_dummy');
            $dbman->drop_table($dummy_table);
        }
    }
}
