<?php

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot . '/local/ssystem/classes/constants/custom_profile_fields.php');
require_once($CFG->dirroot . '/local/ssystem/classes/constants/custom_course_fields.php');
require_once($CFG->dirroot . '/local/ssystem/classes/constants/uf.php');

use \tool_lfxp\helpers\user\profile\custom_profile_category;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field_category;
use \tool_lfxp\helpers\custom_fields\module\custom_course_module_field_category;
use \local_ssystem\constants\custom_profile_fields;
use \local_ssystem\constants\custom_course_fields;
use \local_ssystem\constants\custom_module_fields;
use \local_ssystem\constants\uf;

/**
 * CUSTOM PROFILE FIELDS
 */

function create_custom_profile_fields_category(): custom_profile_category
{
    return custom_profile_category::create(custom_profile_fields::CATEGORY_NAME);
}

function get_custom_profile_fields_category(): ?custom_profile_category
{
    return custom_profile_category::get_by_name(custom_profile_fields::CATEGORY_NAME);
}

function create_cellphone_profile_field(custom_profile_category $category)
{

    $field_raw = [
        'shortname'         => custom_profile_fields::CELLPHONE_FIELD,
        'name'              => 'Celular',
        'datatype'          => 'phone',
        'description'       => '',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 3,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => "br",
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_ufsebrae_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::UFSEBRAE_FIELD,
        'name'              => 'UF SEBRAE',
        'datatype'          => 'menu',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 1,
        'locked'            => 0,
        'visible'           => 2,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => implode("\n", uf::get_sebrae_ufs()),
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_state_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::STATE_FIELD,
        'name'              => 'Estado',
        'datatype'          => 'menu',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 2,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => implode("\n", uf::get_ufs()),
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_status_type_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::STATUS_TYPE_FIELD,
        'name'              => 'Tipo de situação de usuário',
        'datatype'          => 'menu',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 1,
        'locked'            => 1,
        'visible'           => 1,
        'forceunique'       => 0,
        'signup'            => 0,
        'defaultdata'       => custom_profile_fields::STATUS_TYPE_PENDING,
        'defaultdataformat' => 0,
        'param1'            => implode("\n", custom_profile_fields::get_status_type_options()),
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_secondary_email_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::SECONDARY_EMAIL_FIELD,
        'name'              => 'Email secundário',
        'datatype'          => 'email',
        'description'       => '',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 3,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => null,
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = false);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_gender_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::GENDER_FIELD,
        'name'              => 'Gênero',
        'datatype'          => 'menu',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 2,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => implode("\n", custom_profile_fields::get_gender_options()),
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = false);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_birthdate_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::BIRTHDATE_FIELD,
        'name'              => 'Data de nascimento',
        'datatype'          => 'datetime',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 2,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => date('Y', strtotime('-100 years')),
        'param2'            => '2050',
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    $field_raw['startyear'] = $field_raw['param1'];
    $field_raw['endyear'] = $field_raw['param2'];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_registration_date_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::REGISTRATION_DATE_FIELD,
        'name'              => 'Data de cadastro no sistema',
        'datatype'          => 'datetime',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 1,
        'locked'            => 0,
        'visible'           => 2,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => date('Y', strtotime('-40 years')),
        'param2'            => '2050',
        'param3'            => 1,
        'param4'            => null,
        'param5'            => null,
    ];

    $field_raw['startyear'] = $field_raw['param1'];
    $field_raw['endyear'] = $field_raw['param2'];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_expiration_date_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::EXPIRATION_DATE_FIELD,
        'name'              => 'Data de expiração',
        'datatype'          => 'datetime',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 1,
        'locked'            => 0,
        'visible'           => 2,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => date('Y', strtotime('-30 years')),
        'param2'            => '2050',
        'param3'            => 1,
        'param4'            => null,
        'param5'            => null,
    ];

    $field_raw['startyear'] = $field_raw['param1'];
    $field_raw['endyear'] = $field_raw['param2'];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}



function create_occupational_level_field(custom_profile_category $category)
{
    $levels = custom_profile_fields::get_occupational_levels();
    sort($levels);

    $field_raw = [
        'shortname'         => custom_profile_fields::OCCUPATIONAL_LEVEL_FIELD,
        'name'              => 'Nível ocupacional',
        'datatype'          => 'menu',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 3,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => implode("\n", $levels),
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = false);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_occupational_profile_field(custom_profile_category $category)
{

    $field_raw = [
        'shortname'         => custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD,
        'name'              => 'Perfil ocupacional',
        'datatype'          => 'menu',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 0,
        'visible'           => 3,
        'forceunique'       => 0,
        'signup'            => 1,
        'defaultdata'       => '',
        'defaultdataformat' => 0,
        'param1'            => implode("\n", custom_profile_fields::get_occupational_profile_options()),
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = false);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_related_company_profile_field(custom_profile_category $category)
{
    $field_raw = [
        'shortname'         => custom_profile_fields::RELATED_COMPANY_FIELD,
        'name'              => 'Pessoa jurídica relacionada',
        'datatype'          => 'company',
        'description'       => '<p></p>',
        'descriptionformat' => 1,
        'required'          => 0,
        'locked'            => 1,
        'visible'           => 3,
        'forceunique'       => 0,
        'signup'            => 0,
        'defaultdata'       => 0,
        'defaultdataformat' => 0,
        'param1'            => 0,
        'param2'            => null,
        'param3'            => null,
        'param4'            => null,
        'param5'            => null,
    ];

    try {
        $category->create_child($field_raw, $override = true);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}


/**
 * Custom course fields
 */

function create_custom_course_fields_category(): custom_course_field_category
{
    return custom_course_field_category::create(custom_course_fields::CATEGORY_NAME);
}

function get_custom_course_fields_category(): ?custom_course_field_category
{
    return custom_course_field_category::get_by_name(custom_course_fields::CATEGORY_NAME);
}

function create_introduction_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::INTRODUCTION,
        'name' => 'Apresentação',
        'type' => 'textarea',
        'description' => '<p>Apresentação do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_requirements_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::REQUIREMENTS,
        'name' => 'Requisitos',
        'type' => 'textarea',
        'description' => '<p>Requisitos do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_area_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::AREA,
        'name' => 'Área/Subárea',
        'type' => 'textarea',
        'description' => '<p>Área/Subárea do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_complexity_level_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::COMPLEXITY_LEVEL,
        'name' => 'Nível de complexidade',
        'type' => 'select',
        'description' => '',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "options" => implode("\r\n", custom_course_fields::get_complexity_level_options()),
            "defaultvalue" => "",
            "locked" => "0",
            "visibility" => "2"
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_technical_sheet_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::TECHNICAL_SHEET,
        'name' => 'Ficha Técnica',
        'type' => 'textarea',
        'description' => '<p>Ficha técnica do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_evaluation_criteria_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::EVALUATION_CRITERIA,
        'name' => 'Critérios de Avaliação',
        'type' => 'textarea',
        'description' => '<p>Critérios de avaliação do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_objectives_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::OBJECTIVES,
        'name' => 'Objetivos',
        'type' => 'textarea',
        'description' => '<p>Objetivos do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_curriculum_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::CURRICULUM,
        'name' => 'Conteúdo Programático',
        'type' => 'textarea',
        'description' => '<p>Conteúdo programático do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_target_audience_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::TARGET_AUDIENCE,
        'name' => 'Público',
        'type' => 'textarea',
        'description' => '<p>Público do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_consent_term_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::CONSENT_TERM,
        'name' => 'Termo de Aceite',
        'type' => 'textarea',
        'description' => '<p>Termo de aceite do curso</p>',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            'locked' => 0,
            'defaultvalue' => '',
            'defaultvalueformat' => 1,
            'visibility' => 2,
            'required' => 0,
            'uniquevalues' => 0
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_solution_format_course_field(custom_course_field_category $category)
{
    $field_raw = [
        'shortname' => custom_course_fields::SOLUTION_FORMAT,
        'name' => 'Formato da solução',
        'type' => 'select',
        'description' => '',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "options" => implode("\r\n", custom_course_fields::get_solution_format_options()),
            "defaultvalue" => "",
            "locked" => "0",
            "visibility" => "2"
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_uf_course_field(custom_course_field_category $category){
    $field_raw = [
        'shortname' => custom_course_fields::UF,
        'name' => 'UF',
        'type' => 'autocomplete',
        'description' => 'Unidade federativa',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "locked" => "0",
            "visibility" => "2",
            "datatype" => 'list',
            'listvalues' => implode("\n", uf::get_ufs()),
            'defaultvalue' => null,
            'multiple' => 1,
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}


/**
 * Custom course module fields
 */

function create_custom_module_fields_category(): custom_course_module_field_category {
    return custom_course_module_field_category::create(custom_module_fields::CATEGORY_NAME);
}
 
function get_custom_module_fields_category(): ?custom_course_module_field_category {
    return custom_course_module_field_category::get_by_name(custom_module_fields::CATEGORY_NAME);
}

function create_workload_module_field(custom_course_module_field_category $category){
    $field_raw = [
        'shortname' => custom_module_fields::WORKLOAD_FIELD,
        'name' => 'Carga horária',
        'type' => 'duration',
        'description' => 'Carga horária',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "locked" => "0",
            "visibility" => "2",
            "defaultunit" => HOURSECS,
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_uf_module_field(custom_course_module_field_category $category){
    $field_raw = [
        'shortname' => custom_module_fields::UF_FIELD,
        'name' => 'UF',
        'type' => 'autocomplete',
        'description' => 'Unidade federativa',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "locked" => "0",
            "visibility" => "2",
            "datatype" => 'list',
            'listvalues' => implode("\n", uf::get_ufs()),
            'defaultvalue' => null,
            'multiple' => 1,
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_occupational_level_module_field(custom_course_module_field_category $category){
    $field_raw = [
        'shortname' => custom_module_fields::OCCUPATIONAL_LEVEL_FIELD,
        'name' => 'Nível Ocupacional',
        'type' => 'autocomplete',
        'description' => '',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "locked" => "0",
            "visibility" => "2",
            "datatype" => 'list',
            'listvalues' => implode("\n", custom_module_fields::get_occupational_levels_options()),
            'defaultvalue' => null,
            'multiple' => 1,
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_solution_format_module_field(custom_course_module_field_category $category){
    $field_raw = [
        'shortname' => custom_module_fields::SOLUTION_FORMAT_FIELD,
        'name' => 'Formato de Solução',
        'type' => 'autocomplete',
        'description' => '',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "locked" => "0",
            "visibility" => "2",
            "datatype" => 'list',
            'listvalues' => implode("\n", custom_module_fields::get_solution_format_options()),
            'defaultvalue' => null,
            'multiple' => 1,
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}

function create_subject_module_field(custom_course_module_field_category $category){
    $field_raw = [
        'shortname' => custom_module_fields::SUBJECT_FIELD,
        'name' => 'Temática',
        'type' => 'autocomplete',
        'description' => '',
        'descriptionformat' => 1,
        'configdata' => json_encode([
            "required" => "0",
            "uniquevalues" => "0",
            "locked" => "0",
            "visibility" => "2",
            "datatype" => 'sql',
            'sqlquery' => "SELECT id, name FROM {course_categories} WHERE parent > 0 AND visible = 1 ORDER BY sortorder ASC",
            'defaultvalue' => null,
            'multiple' => 0,
        ]),
    ];

    try {
        $category->create_field($field_raw);
    } catch (\Throwable $th) {
        debugging($th->getMessage(), DEBUG_DEVELOPER);
    }
}



/**
 * Install functions (called during install and by the installer CLI)
 */


function install_local_ssystem_custom_profile_fields(){
    if (!$custom_profile_fields_category = get_custom_profile_fields_category()) {
        $custom_profile_fields_category = create_custom_profile_fields_category();
    }
    
    create_cellphone_profile_field($custom_profile_fields_category);
    create_ufsebrae_profile_field($custom_profile_fields_category);
    create_state_profile_field($custom_profile_fields_category);
    create_status_type_profile_field($custom_profile_fields_category);
    create_secondary_email_profile_field($custom_profile_fields_category);
    create_gender_profile_field($custom_profile_fields_category);
    create_birthdate_profile_field($custom_profile_fields_category);
    create_registration_date_profile_field($custom_profile_fields_category);
    create_occupational_level_field($custom_profile_fields_category);
    create_occupational_profile_field($custom_profile_fields_category);
    create_related_company_profile_field($custom_profile_fields_category);
    create_expiration_date_profile_field($custom_profile_fields_category);
}


function install_local_ssystem_custom_course_fields(){
    if (!$custom_course_fields_category = get_custom_course_fields_category()) {
        $custom_course_fields_category = create_custom_course_fields_category();
    }
    
    create_introduction_course_field($custom_course_fields_category);
    create_requirements_course_field($custom_course_fields_category);
    create_area_course_field($custom_course_fields_category);
    create_complexity_level_course_field($custom_course_fields_category);
    create_technical_sheet_course_field($custom_course_fields_category);
    create_evaluation_criteria_course_field($custom_course_fields_category);
    create_objectives_course_field($custom_course_fields_category);
    create_curriculum_course_field($custom_course_fields_category);
    create_target_audience_course_field($custom_course_fields_category);
    create_consent_term_course_field($custom_course_fields_category);
    create_solution_format_course_field($custom_course_fields_category);
    create_uf_course_field($custom_course_fields_category);
}

function install_local_ssystem_custom_module_fields(){
    if (!$custom_course_fields_category = get_custom_module_fields_category()) {
        $custom_course_fields_category = create_custom_module_fields_category();
    }

    create_workload_module_field($custom_course_fields_category);
    create_uf_module_field($custom_course_fields_category);
    create_occupational_level_module_field($custom_course_fields_category);
    create_solution_format_module_field($custom_course_fields_category);
    create_subject_module_field($custom_course_fields_category);
}