<?php namespace local_ssystem\constants;

abstract class custom_profile_fields{
    const CATEGORY_NAME = "Campos Personalizados";

    const GENDER_FIELD = "genero"; // Gênero
    const BIRTHDATE_FIELD = "data_de_nascimento"; // Data de nascimento
    const UFSEBRAE_FIELD = "ufsebrae"; // UF
    const STATE_FIELD = "estado"; // Estado (UF)
    const SECONDARY_EMAIL_FIELD = "email_secundario"; // Email secundário
    const CELLPHONE_FIELD = "celular"; // Celular
    const REGISTRATION_DATE_FIELD = "data_de_cadastro"; // Data de cadastro no sistema (SEBRAE)
    const EXPIRATION_DATE_FIELD = "data_de_expiracao"; // Data de expiração (SEBRAE)
    const OCCUPATIONAL_PROFILE_FIELD = "perfil_ocupacional"; // Perfil ocupacional
    const OCCUPATIONAL_LEVEL_FIELD = "nivel_ocupacional"; // Nível ocupacional
    const STATUS_TYPE_FIELD = "situacao_de_usuario"; // Tipo de situação de usuário
    const RELATED_COMPANY_FIELD = "pessoa_juridica_relacionada"; // Pessoa jurídica a qual está relacionado

    const STATUS_TYPE_INACTIVE = 'inativo';
    const STATUS_TYPE_PENDING = 'pendente';
    const STATUS_TYPE_ACTIVE = 'ativo';

    const OCCUPATIONAL_PROFILE_WORKER = 'colaborador';
    const OCCUPATIONAL_PROFILE_OUTSOURCE = 'terceiro';

    const GENDER_MALE = 'masculino';
    const GENDER_FEMALE = 'feminino';
    const GENDER_OTHER = 'outros';

    protected static array $occupational_levels = [];

    public static function get_status_type_options() : array {
        return [
            self::STATUS_TYPE_INACTIVE,
            self::STATUS_TYPE_PENDING,
            self::STATUS_TYPE_ACTIVE,
        ];
    }

    public static function get_gender_options() : array {
        return [
            self::GENDER_MALE,
            self::GENDER_FEMALE,
            self::GENDER_OTHER,
        ];
    }

    public static function get_occupational_profile_options() : array {
        return [
            self::OCCUPATIONAL_PROFILE_WORKER,
            self::OCCUPATIONAL_PROFILE_OUTSOURCE,
        ];
    }

    const OCCUPATIONAL_LEVELS_BY_PROFILE = [
        self::OCCUPATIONAL_PROFILE_WORKER => [
            'JOVEM APRENDIZ',
            'DIRIGENTE',
            'ANALISTA',
            'ASSISTENTE',
            'ESTAGIÁRIO',
            'CONSELHEIRO',
            'TRAINEE',
            'TEMPORÁRIO/TERCEIRIZADO',
            'GERENTE',
            'GERENTE ADJUNTO',
            'ASSESSOR',
            'CHEFE DE GABINETE',
            'COORDENADOR'
        ],
        self::OCCUPATIONAL_PROFILE_OUTSOURCE => [
            'PARCEIRO',
            'AGENTE DE DESENVOLVIMENTO',
            'CONSELHEIRO',
            'AGENTE DE ORIENTAÇÃO EMPRESARIAL',
            'AGENTE LOCAL DE INOVAÇÃO',
            'CREDENCIADO',
            'ORIENTADOR ALI',
            'AGENTE DE PONTO DE ATENDIMENTO',
            'AGENTE DA CENTRAL DE ATENDIMENTO',
            'AGENTE DE SALA DO EMPREENDEDOR',
        ],
    ];

    public static function get_occupational_levels(?string $occupational_profile = null) : array {
        if(isset(self::OCCUPATIONAL_LEVELS_BY_PROFILE[$occupational_profile])){
            return self::OCCUPATIONAL_LEVELS_BY_PROFILE[$occupational_profile];
        }

        if(empty(self::$occupational_levels)){
            self::$occupational_levels = array_unique(array_merge(...array_values(self::OCCUPATIONAL_LEVELS_BY_PROFILE)));
            self::$occupational_levels[] = 'NÃO IDENTIFICADO';
        }

        return self::$occupational_levels;
    }
}