<?php

require_once(__DIR__ . '/../../../config.php');

$title = "Alunos";
$PAGE->set_context(context_system::instance());
$PAGE->set_url(new moodle_url('/local/legacy/report/test.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'alunos';

$report = new \local_legacy\table\persistent_table(
    $tableid,
    \local_legacy\models\aluno::class
);

// Let the user download if requested.
$report->is_downloading($download, 'alunos_export', $tableid);

// Omit any internal or unwanted columns.
$report->omit_columns(['id', 'timemodified', 'timecreated', 'usermodified', 'hash']);

$report->define_baseurl($PAGE->url);
// $report->set_filters(['status' => 'active']);
// $report->set_sort('id', 'ASC');

// Only output header if not exporting.
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// Render the table (25 rows per page, no initials bar).
$report->out(25, false);

// Footer for HTML view.
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
