<?php

namespace local_legacy\models\certificate;

use core\persistent;
use local_legacy\models\abstract_legacy_entity;
use local_legacy\models\aluno;
use local_legacy\models\interfaces\certificate_issue_interface;
use local_legacy\models\interfaces\situacao_matricula_interface;
use local_legacy\models\situacao_curso;


/**
 * Certificates issued before EADTECH
 */
class old_certificate_issue extends persistent implements certificate_issue_interface {

    const TABLE = 'legacy_old_cert_issues';

    protected static function define_properties(): array {
        return [
            'codsituacaoalunocurso' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
            ],
            'code' => [
                'type' => PARAM_RAW,
                'description' => 'Certificate validation code.',
                'null' => NULL_NOT_ALLOWED,
            ],
        ];
    }

    public function get_situacao_matricula() : ?situacao_matricula_interface {
        return situacao_curso::get_record(['codsituacaoalunocurso' => $this->get('codsituacaoalunocurso')]) ?: null;
    }

    public static function get_by_code(string $code) : ?static {
        return self::get_record(['code' => $code]) ?: null;
    }
}
