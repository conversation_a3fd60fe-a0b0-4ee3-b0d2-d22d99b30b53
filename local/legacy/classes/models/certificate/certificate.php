<?php

namespace local_legacy\models\certificate;

use core\persistent;
use local_legacy\models\abstract_legacy_entity;
use local_legacy\models\curso;
use local_legacy\models\trilha;

class certificate extends persistent {

    const TABLE = 'legacy_certificate';

    const TYPE_COURSE = 'course';
    const TYPE_TRAIL = 'trail';

    /**
     * Return the definition of properties.
     *
     * @return array
     */
    protected static function define_properties(): array {
        return [
            'solutiontype' => [
                'type' => PARAM_TEXT,
                'description' => 'Type of solution: course or trail.',
                'null' => NULL_NOT_ALLOWED,
                'choices' => [
                    self::TYPE_COURSE,
                    self::TYPE_TRAIL,
                ],
            ],
            'solutionid' => [
                'type' => PARAM_INT,
                'description' => 'ID of the course or trail.',
                'null' => NULL_NOT_ALLOWED,
            ],
            'legacyid' => [
                'type' => PARAM_INT,
                'description' => 'Original certificate ID.',
                'null' => NULL_NOT_ALLOWED,
            ],
            'name' => [
                'type' => PARAM_TEXT,
                'description' => 'Certificate name.',
                'null' => NULL_NOT_ALLOWED,
            ],
            'width' => [
                'type' => PARAM_INT,
                'description' => 'Page width (mm).',
                'default' => 297,
                'null' => NULL_NOT_ALLOWED,
            ],
            'height' => [
                'type' => PARAM_INT,
                'description' => 'Page height (mm).',
                'default' => 210,
                'null' => NULL_NOT_ALLOWED,
            ],
            'certificateimage' => [
                'type' => PARAM_RAW,
                'description' => 'Certificate background image (raw data).',
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'certificatetext' => [
                'type' => PARAM_RAW,
                'description' => 'Main certificate text (HTML).',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'certificatetextformat' => [
                'type' => PARAM_INT,
                'description' => 'Format of certificatetext.',
                'default' => 2,
                'null' => NULL_NOT_ALLOWED,
            ],
            'certificatetextx' => [
                'type' => PARAM_INT,
                'description' => 'X position for text.',
                'default' => 10,
                'null' => NULL_NOT_ALLOWED,
            ],
            'certificatetexty' => [
                'type' => PARAM_INT,
                'description' => 'Y position for text.',
                'default' => 50,
                'null' => NULL_NOT_ALLOWED,
            ],
            'outcome' => [
                'type' => PARAM_INT,
                'description' => 'Outcome flag.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'certdate' => [
                'type' => PARAM_INT,
                'description' => 'Date display option.',
                'default' => -2,
                'null' => NULL_NOT_ALLOWED,
            ],
            'certdatefmt' => [
                'type' => PARAM_TEXT,
                'description' => 'Date format string.',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'certgrade' => [
                'type' => PARAM_INT,
                'description' => 'Show grade flag.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'gradefmt' => [
                'type' => PARAM_INT,
                'description' => 'Grade format option.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'printqrcode' => [
                'type' => PARAM_BOOL,
                'description' => 'Whether to print a QR code.',
                'default' => true,
                'null' => NULL_NOT_ALLOWED,
            ],
            'qrcodefirstpage' => [
                'type' => PARAM_BOOL,
                'description' => 'Print QR code on first page only.',
                'default' => false,
                'null' => NULL_NOT_ALLOWED,
            ],
            'codex' => [
                'type' => PARAM_INT,
                'description' => 'X position for QR code.',
                'default' => 10,
                'null' => NULL_NOT_ALLOWED,
            ],
            'codey' => [
                'type' => PARAM_INT,
                'description' => 'Y position for QR code.',
                'default' => 10,
                'null' => NULL_NOT_ALLOWED,
            ],
            'enablesecondpage' => [
                'type' => PARAM_BOOL,
                'description' => 'Enable second page.',
                'default' => false,
                'null' => NULL_NOT_ALLOWED,
            ],
            'secondpagex' => [
                'type' => PARAM_INT,
                'description' => 'X position for second page text.',
                'default' => 10,
                'null' => NULL_ALLOWED,
            ],
            'secondpagey' => [
                'type' => PARAM_INT,
                'description' => 'Y position for second page text.',
                'default' => 50,
                'null' => NULL_ALLOWED,
            ],
            'secondpagetext' => [
                'type' => PARAM_RAW,
                'description' => 'Text for second page (HTML).',
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'secondpagetextformat' => [
                'type' => PARAM_INT,
                'description' => 'Format of second page text.',
                'default' => 2,
                'null' => NULL_ALLOWED,
            ],
            'secondimage' => [
                'type' => PARAM_RAW,
                'description' => 'Image for second page (raw data).',
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'timestartdatefmt' => [
                'type' => PARAM_TEXT,
                'description' => 'Format for start date on timestamp.',
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
        ];
    }

    public function is_solutiontype_course() : bool {
        return $this->get('solutiontype') == self::TYPE_COURSE;
    }

    public function is_solutiontype_trail() : bool {
        return $this->get('solutiontype') == self::TYPE_TRAIL;
    }

    public function get_solution() : ?abstract_legacy_entity {
        if(!$solutionid = (int) $this->get('solutionid')){
            return null;
        }

        if($this->is_solutiontype_course()){
            return curso::get_by_codcurso($solutionid);
        }

        if($this->is_solutiontype_trail()){
            return trilha::get_by_codtrilha($solutionid);
        }

        return null;
    }
}