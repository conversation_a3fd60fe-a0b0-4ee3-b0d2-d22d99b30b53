<?php

namespace local_legacy\models\certificate;

use core\persistent;
use local_legacy\models\abstract_legacy_entity;
use local_legacy\models\aluno;
use local_legacy\models\certificate\certificate;
use local_legacy\models\interfaces\certificate_issue_interface;
use local_legacy\models\interfaces\situacao_matricula_interface;
use local_legacy\models\situacao_curso;
use local_legacy\models\situacao_trilha;

class certificate_issue extends persistent implements certificate_issue_interface {

    const TABLE = 'legacy_certificate_issues';

    protected certificate $certificate;

    protected static function define_properties(): array {
        return [
            'certificateid' => [
                'type' => PARAM_INT,
                'description' => 'ID of the certificate.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'codaluno' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
            ],
            'codturma' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
            ],
            'code' => [
                'type' => PARAM_RAW,
                'description' => 'Certificate validation code.',
                'null' => NULL_NOT_ALLOWED,
            ],
        ];
    }

    public function get_user() : ?abstract_legacy_entity {
        return aluno::get_by_codaluno((int) $this->get('codaluno'));
    }

    public static function get_by_code(string $code) : ?static {
        return self::get_record(['code' => $code]) ?: null;
    }

    public function get_certificate() : certificate {
        if(!isset($this->certificate)){
            $this->certificate = new certificate((int) $this->get('certificateid'));
        }
        return $this->certificate;
    }

    public function get_situacao_matricula() : ?situacao_matricula_interface {
        $certificate = $this->get_certificate();

        $conditions = [
            'codaluno' => $this->get('codaluno'),
            'codturma' => $this->get('codturma'),
        ];

        if($certificate->is_solutiontype_course()){
            $conditions['codcurso'] = $certificate->get('coditem');
            return situacao_curso::get_record($conditions) ?: null;
        }

        $conditions['codtrilha'] = $certificate->get('codtrilha');
        return situacao_trilha::get_record($conditions) ?: null;
    }
}