<?php

namespace local_legacy\models;

use local_legacy\util\eadtech_helper;


defined('MOODLE_INTERNAL') || die();

class feedback_v1 extends abstract_legacy_entity {
    const TABLE = 'legacy_feedback_v1';

    public static function define_identifier(): string|array {
        return ['codaluno', 'codcurso'];
    }

    protected static function define_model_properties(): array {
        return [
            'codaluno' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codcurso' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codperresfeedback' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'resposta' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'datahora' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'justificativa' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'codturma' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'pergunta' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'pesopergunta' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codgrupofeedback' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'ordem' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
        ];
    }
}
