<?php

namespace local_legacy\models;

use local_legacy\util\eadtech_helper;
use local_legacy\models\interfaces\situacao_matricula_interface;


class situacao_trilha extends abstract_legacy_entity implements situacao_matricula_interface {
    const TABLE = 'legacy_situacao_trilha';

    public static function define_identifier(): string|array {
        return 'codsituacao';
    }

    protected static function define_model_properties(): array {
        return [
            'codsituacao' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codtrilha' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codturma' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codaluno' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'datamatricula' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'percconclusao' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_float_formatter'],
            ],
            'percaproveitamento' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_float_formatter'],
            ],
            'prazodeacesso' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'origemmatricula' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datainicio' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'datafim' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'statusmatricula' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'progressstatus' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'nota_do_usuario' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_float_formatter'],
            ],
            'data_do_primeiro_acesso' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'data_do_ultimo_acesso' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'data_de_conclusao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'hash' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
            ],
        ];
    }

    public static function get_most_recent($coditem, $codaluno, $codturma) : ?static {
        $conditions = [
            'codtrilha' => $coditem,
            'codaluno' => $codaluno,
            'codturma' => $codturma,
        ];
        $instances = static::get_records($conditions, static::define_identifier(), 'DESC');
        if(!empty($instances)){
            return reset($instances);
        }
        return null;
    }

    public static function upsert_from_migration_table_data(array|object $data) : int {
        $data['codsituacao'] = $data['source_id'];
        $data = self::remove_key_prefix((array) $data, 'source_');
        unset($data['custom_data']);
        unset($data['lock_expires_at']);
        unset($data['locked_by']);
        return static::upsert_from_csv($data);
    }
}
