<?php namespace local_legacy\models;

use core\persistent;
use Generator;
use coding_exception;
use local_legacy\util\eadtech_helper;

defined('MOODLE_INTERNAL') || die();

abstract class abstract_legacy_entity extends persistent {

    /**
     * @throws coding_exception If a property key from define_model_properties does not start with 'source_'.
     * @return array
     */
    protected static function define_properties(): array {
        $definitions = static::define_model_properties();

        return array_merge($definitions, [
            'hash' => [
                'type' => PARAM_RAW,
                'default' => '',
            ],
        ]);
    }

    public static function generate_hash(array|object $data) : string {
        $data = (array) $data;
        ksort($data);
        return hash('md5', json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
    }

    /**
     * Defines the entity-specific properties that should be merged into the persistent definition.
     *
     * This method must be implemented by child classes to specify custom fields.
     *
     * @return array Array of entity-specific property definitions.
     */
    abstract protected static function define_model_properties(): array;

    abstract public static function define_identifier(): string|array;

    protected static function format_csv_data(array $data): array {
        foreach (static::define_model_properties() as $key => $definition) {
            if (!array_key_exists($key, $data)) {
                continue;
            }

            if (!empty($definition['formatter']) && is_callable($definition['formatter'])) {
                $default = $definition['default'] ?? null;
                call_user_func_array($definition['formatter'], [&$data, $key, $default]);
            }
        }

        return $data;
    }


    /**
     * @param array $data
     * @return integer record ID
     */
    public static function upsert_from_csv(array $data) : int {
        global $DB;

        if(!empty($data['hash'])){
            $hash = $data['hash'];
        }else{
            $hash = static::generate_hash($data);
        }
        
        $data = static::format_csv_data($data);

        $allowed_keys = array_keys(static::define_properties());
        $data = array_intersect_key($data, array_flip($allowed_keys));

        $identifier = static::define_identifier();
        if(is_string($identifier)){
            $identifier = [$identifier];
        }

        $existing_conditions = [];
        foreach ($identifier as $column) {
            if(empty($data[$column])){
                return 0;
            }
            $existing_conditions[$column] = $data[$column];
        }

        unset($data['id']);

        foreach ($data as $key => $value) {
            if($value === null || strtoupper(trim($value)) == "NULL"){
                unset($data[$key]);
            }
        }

        $existing = $DB->get_record(static::TABLE, $existing_conditions, 'id, hash');

        if(!$existing){
            $data['hash'] = $hash;
            $id = $DB->insert_record(static::TABLE, $data);
            return $id;
        }

        if($hash != $existing->hash){
            $data['id'] = $existing->id;
            $data['hash'] = $hash;
            $DB->update_record(static::TABLE, $data);
            return $existing->id;
        }

        return 0;
    }

    /**
    * Removes a prefix from the keys of an associative array.
    *
    * @param array $array The original array.
    * @param string $prefix The prefix to remove.
    * @return array The new array with the updated keys.
    */
    public static function remove_key_prefix(array $data, string $prefix) : array {
        $prefix_length = strlen($prefix);

        return array_combine(
            array_map(
                fn($key) => str_starts_with($key, $prefix) ? substr($key, $prefix_length) : $key,
                array_keys($data)
            ),
            array_values($data)
        );
    }

    public static function upsert_from_migration_table_data(array|object $data) : int {
        $data = self::remove_key_prefix((array) $data, 'source_');
        unset($data['custom_data']);
        unset($data['lock_expires_at']);
        unset($data['locked_by']);
        return static::upsert_from_csv($data);
    }
}
