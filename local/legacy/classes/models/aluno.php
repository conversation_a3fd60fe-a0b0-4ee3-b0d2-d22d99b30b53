<?php

namespace local_legacy\models;

use local_legacy\util\eadtech_helper;


defined('MOODLE_INTERNAL') || die();

class aluno extends abstract_legacy_entity {
    const TABLE = 'legacy_alunos';

    public static function define_identifier(): string {
        return 'codaluno';
    }

    protected static function define_model_properties(): array {
        return [
            'userid' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codaluno' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'nome' => [
                'type' => PARAM_TEXT,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'email' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'estado' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'sexo' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'nascimento' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'escolaridade' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'estadocivil' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datacadastro' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'nivelocupacional' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'cpf' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'emailsecundario' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'celular' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'perfil' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'status' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'filial' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'dataadmissao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'dataexpiracao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'dataalteracao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'ufsebrae' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datamodificacao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
        ];
    }

    public static function get_by_codaluno(int $codaluno) : ?static {
        return static::get_record(['codaluno' => $codaluno]) ?: null;
    }
}
