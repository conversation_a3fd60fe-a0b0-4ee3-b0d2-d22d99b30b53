<?php

namespace local_legacy\models;

use local_legacy\util\eadtech_helper;
use local_legacy\models\interfaces\turma_interface;



defined('MOODLE_INTERNAL') || die();

class turma_trilha extends abstract_legacy_entity implements turma_interface {
    const TABLE = 'legacy_turmas_trilhas';

    public static function define_identifier(): string {
        return 'codturma';
    }

    protected static function define_model_properties(): array {
        return [
            'codturma' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codtrilha' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'nometurma' => [
                'type' => PARAM_TEXT,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datainicio' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'datafim' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'numeromaximoalunos' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'criado' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'modificado' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'disponivel' => [
                'type' => PARAM_BOOL,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_bool_formatter'],
            ],
            'descricao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datainicioprematricula' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
        ];
    }
}
