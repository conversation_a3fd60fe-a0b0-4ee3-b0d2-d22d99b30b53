<?php namespace local_legacy\certificate\forms;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');
require_once($CFG->dirroot . '/grade/lib.php');


use context_system;
use core\context;
use grade_tree;
use local_legacy\certificate\certificate;

class edit_form extends \moodleform {

    protected context $context;

    public function __construct($action=null, $customdata=null, $method='post', $target='', $attributes=null, $editable=true, $ajaxformdata=null) {
        parent::__construct($action, $customdata, $method, $target, $attributes, $editable, $ajaxformdata);

        $this->context = context_system::instance();
    }

    protected function get_editor_options(object $context) {
        return [
            'subdirs' => 0, 'maxbytes' => 0,
            'maxfiles' => 0, 'changeformat' => 0,
            'context' => $context, 'noclean' => 0,
            'trusttext' => 0
        ];
    }

    protected function get_outcomes() {
        global $COURSE;

        // Get all outcomes in course.
        $gradeseq = new grade_tree($COURSE->id, false, true, '', false);
        $gradeitems = $gradeseq->items;
        if ($gradeitems) {
            // List of item for menu.
            $printoutcome = array();
            foreach ($gradeitems as $gradeitem) {
                if (isset($gradeitem->outcomeid)) {
                    $itemmodule = $gradeitem->itemmodule;
                    $printoutcome[$gradeitem->id] = $itemmodule . ': ' . $gradeitem->get_name();
                }
            }
        }
        if (isset($printoutcome)) {
            $outcomeoptions['0'] = get_string('no');
            foreach ($printoutcome as $key => $value) {
                $outcomeoptions[$key] = $value;
            }
        } else {
            $outcomeoptions['0'] = get_string('nooutcomes', 'grades');
        }

        return $outcomeoptions;
    }

    /**
    * Search through all the modules for grade dates for mod_form.
    *
    * @return array
    */
    function get_date_options() {
        global $CFG;
        $dateoptions[certificate::CERT_ISSUE_DATE] = get_string('certificate:issueddate', 'local_legacy');
        $dateoptions[certificate::COURSE_COMPLETATION_DATE] = get_string('certificate:completiondate', 'local_legacy');
        $dateoptions[certificate::COURSE_START_DATE] = get_string('certificate:coursestartdate', 'local_legacy');
        return $dateoptions;
    }

    /**
     * Search through all the modules for grade data for mod_form.
     *
     * @return array
     */
    function get_grade_options() {
        $gradeoptions[certificate::NO_GRADE] = get_string('nograde');
        $gradeoptions[certificate::COURSE_GRADE] = get_string('certificate:coursegrade', 'local_legacy');
        return $gradeoptions;
    }

    public function definition() {
        global $CFG, $COURSE;
        $mform = $this->_form;

        // Hidden ID field for create/edit.
        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        // General options.
        $mform->addElement('header', 'general', get_string('general', 'form'));
        $mform->addElement('text', 'name', get_string('certificate:certificatename', 'local_legacy'), ['size' => 64]);
        $mform->setType('name', PARAM_TEXT);
        $mform->addRule('name', null, 'required', null, 'client');
        $mform->addHelpButton('name', 'certificate:certificatename', 'local_legacy');

        // Design Options.
        $mform->addElement('header', 'designoptions', get_string('certificate:designoptions', 'local_legacy'));
        $mform->addElement('filemanager', 'certificateimage',
            get_string('certificate:certificateimage', 'local_legacy'), null,
            ['subdirs' => 0, 'maxbytes' => $COURSE->maxbytes, 'maxfiles' => 1, 'accepted_types' => ['image']]
        );
        $mform->addHelpButton('certificateimage', 'certificate:certificateimage', 'local_legacy');

        $mform->addElement('editor', 'certificatetext',
            get_string('certificate:certificatetext', 'local_legacy'), null,
            $this->get_editor_options(context_system::instance())
        );
        $mform->addRule('certificatetext', get_string('certificate:certificatetexterror', 'local_legacy'), 'required', null, 'client');
        $mform->addHelpButton('certificatetext', 'certificate:certificatetext', 'local_legacy');

        $mform->addElement('text', 'width', get_string('certificate:width', 'local_legacy'), ['size' => 5]);
        $mform->setType('width', PARAM_INT);
        $mform->setDefault('width', get_config('local_legacy', 'width'));
        $mform->setAdvanced('width');
        $mform->addHelpButton('width', 'certificate:size', 'local_legacy');

        $mform->addElement('text', 'height', get_string('certificate:height', 'local_legacy'), ['size' => 5]);
        $mform->setType('height', PARAM_INT);
        $mform->setDefault('height', get_config('local_legacy', 'height'));
        $mform->setAdvanced('height');
        $mform->addHelpButton('height', 'certificate:size', 'local_legacy');

        $mform->addElement('text', 'certificatetextx', get_string('certificate:certificatetextx', 'local_legacy'), ['size' => 5]);
        $mform->setType('certificatetextx', PARAM_INT);
        $mform->setDefault('certificatetextx', get_config('local_legacy', 'certificatetextx'));
        $mform->setAdvanced('certificatetextx');
        $mform->addHelpButton('certificatetextx', 'certificate:textposition', 'local_legacy');

        $mform->addElement('text', 'certificatetexty', get_string('certificate:certificatetexty', 'local_legacy'), ['size' => 5]);
        $mform->setType('certificatetexty', PARAM_INT);
        $mform->setDefault('certificatetexty', get_config('local_legacy', 'certificatetexty'));
        $mform->setAdvanced('certificatetexty');
        $mform->addHelpButton('certificatetexty', 'certificate:textposition', 'local_legacy');

        // Second page options.
        $mform->addElement('header', 'secondpageoptions', get_string('certificate:secondpageoptions', 'local_legacy'));
        $mform->addElement('selectyesno', 'enablesecondpage', get_string('certificate:enablesecondpage', 'local_legacy'));
        $mform->setDefault('enablesecondpage', get_config('local_legacy', 'enablesecondpage'));
        $mform->addHelpButton('enablesecondpage', 'certificate:enablesecondpage', 'local_legacy');

        $mform->addElement('filemanager', 'secondimage',
            get_string('certificate:secondimage', 'local_legacy'), null,
            ['subdirs' => 0, 'maxbytes' => $COURSE->maxbytes, 'maxfiles' => 1, 'accepted_types' => ['image']]
        );
        $mform->addHelpButton('secondimage', 'certificate:secondimage', 'local_legacy');
        $mform->disabledIf('secondimage', 'enablesecondpage', 'eq', 0);

        $mform->addElement('editor', 'secondpagetext',
            get_string('certificate:secondpagetext', 'local_legacy'), null,
            $this->get_editor_options(context_system::instance())
        );
        $mform->addHelpButton('secondpagetext', 'certificate:secondpagetext', 'local_legacy');
        $mform->disabledIf('secondpagetext', 'enablesecondpage', 'eq', 0);

        $mform->addElement('text', 'secondpagex', get_string('certificate:secondpagex', 'local_legacy'), ['size' => 5]);
        $mform->setType('secondpagex', PARAM_INT);
        $mform->setDefault('secondpagex', get_config('local_legacy', 'certificatetextx'));
        $mform->setAdvanced('secondpagex');
        $mform->addHelpButton('secondpagex', 'certificate:secondtextposition', 'local_legacy');
        $mform->disabledIf('secondpagex', 'enablesecondpage', 'eq', 0);

        $mform->addElement('text', 'secondpagey', get_string('certificate:secondpagey', 'local_legacy'), ['size' => 5]);
        $mform->setType('secondpagey', PARAM_INT);
        $mform->setDefault('secondpagey', get_config('local_legacy', 'certificatetexty'));
        $mform->setAdvanced('secondpagey');
        $mform->addHelpButton('secondpagey', 'certificate:secondtextposition', 'local_legacy');
        $mform->disabledIf('secondpagey', 'enablesecondpage', 'eq', 0);

        // Variable options.
        $mform->addElement('header', 'variablesoptions', get_string('certificate:variablesoptions', 'local_legacy'));
        $mform->addElement('text', 'coursename', get_string('certificate:coursename', 'local_legacy'), ['size' => 64]);
        $mform->setType('coursename', PARAM_TEXT);
        $mform->setAdvanced('coursename');
        $mform->addHelpButton('coursename', 'certificate:coursename', 'local_legacy');

        $outcomeoptions = $this->get_outcomes();
        $mform->addElement('select', 'outcome', get_string('certificate:printoutcome', 'local_legacy'), $outcomeoptions);
        $mform->setDefault('outcome', 0);
        $mform->addHelpButton('outcome', 'certificate:printoutcome', 'local_legacy');

        $dateoptions = $this->get_date_options();
        $mform->addElement('select', 'certdate', get_string('certificate:printdate', 'local_legacy'), $dateoptions);
        $mform->setDefault('certdate', get_config('local_legacy', 'certdate'));
        $mform->addHelpButton('certdate', 'certificate:printdate', 'local_legacy');

        $mform->addElement('text', 'certdatefmt', get_string('certificate:datefmt', 'local_legacy'));
        $mform->setType('certdatefmt', PARAM_TEXT);
        $mform->setDefault('certdatefmt', '');
        $mform->setAdvanced('certdatefmt');
        $mform->addHelpButton('certdatefmt', 'certificate:datefmt', 'local_legacy');

        $mform->addElement('text', 'timestartdatefmt', get_string('certificate:timestartdatefmt', 'local_legacy'));
        $mform->setType('timestartdatefmt', PARAM_TEXT);
        $mform->setDefault('timestartdatefmt', '');
        $mform->setAdvanced('timestartdatefmt');
        $mform->addHelpButton('timestartdatefmt', 'certificate:timestartdatefmt', 'local_legacy');

        $mform->addElement('select', 'certgrade', get_string('certificate:printgrade', 'local_legacy'), $this->get_grade_options());
        $mform->setDefault('certgrade', 0);
        $mform->addHelpButton('certgrade', 'certificate:printgrade', 'local_legacy');

        $gradefmtoptions = [1 => get_string('certificate:gradepercent', 'local_legacy'), 2 => get_string('certificate:gradepoints', 'local_legacy'), 3 => get_string('certificate:gradeletter', 'local_legacy')];
        $mform->addElement('select', 'gradefmt', get_string('certificate:gradefmt', 'local_legacy'), $gradefmtoptions);
        $mform->setDefault('gradefmt', 0);
        $mform->addHelpButton('gradefmt', 'certificate:gradefmt', 'local_legacy');

        $mform->addElement('selectyesno', 'printqrcode', get_string('certificate:printqrcode', 'local_legacy'));
        $mform->setDefault('printqrcode', get_config('local_legacy', 'printqrcode'));
        $mform->addHelpButton('printqrcode', 'certificate:printqrcode', 'local_legacy');

        $mform->addElement('text', 'codex', get_string('certificate:codex', 'local_legacy'), ['size'=>5]);
        $mform->setType('codex', PARAM_INT);
        $mform->setDefault('codex', get_config('local_legacy', 'codex'));
        $mform->setAdvanced('codex');
        $mform->addHelpButton('codex', 'certificate:qrcodeposition', 'local_legacy');

        $mform->addElement('text', 'codey', get_string('certificate:codey', 'local_legacy'), ['size'=>5]);
        $mform->setType('codey', PARAM_INT);
        $mform->setDefault('codey', get_config('local_legacy', 'codey'));
        $mform->setAdvanced('codey');
        $mform->addHelpButton('codey', 'certificate:qrcodeposition', 'local_legacy');

        $mform->addElement('selectyesno', 'qrcodefirstpage', get_string('certificate:qrcodefirstpage', 'local_legacy'));
        $mform->setDefault('qrcodefirstpage', get_config('local_legacy', 'qrcodefirstpage'));
        $mform->addHelpButton('qrcodefirstpage', 'certificate:qrcodefirstpage', 'local_legacy');

        // Action buttons.
        $this->add_action_buttons(true);
    }

    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        return $errors;
    }

    /**
     * Prepares the form before data are set
     *
     * Additional wysiwyg editor are prepared here, the introeditor is prepared automatically by core.
     * Grade items are set here because the core modedit supports single grade item only.
     *
     * @param array $data to be set
     * @return void
     */
    public function data_preprocessing(&$data) {
        global $CFG;

        parent::data_preprocessing($data);
        if ($this->current->instance) {
            // Editing an existing certificate - let us prepare the added editor elements (intro done automatically), and files.
            // First Page.
            // Get firstimage.
            $imagedraftitemid = file_get_submitted_draft_itemid('certificateimage');
            // Get firtsimage filearea information.
            $imagefileinfo = certificate::get_certificate_image_fileinfo($this->context);
            file_prepare_draft_area($imagedraftitemid, $imagefileinfo['contextid'],
                            $imagefileinfo['component'], $imagefileinfo['filearea'],
                            $imagefileinfo['itemid'],
                            $this->get_filemanager_options_array());

            $data['certificateimage'] = $imagedraftitemid;

            // Prepare certificate text.
            $data['certificatetext'] = array('text' => $data['certificatetext'], 'format' => FORMAT_HTML);

            // Second page.
            // Get Back image.
            $secondimagedraftitemid = file_get_submitted_draft_itemid('secondimage');
            // Get secondimage filearea info.
            $secondimagefileinfo = certificate::get_certificate_secondimage_fileinfo($this->context);
            file_prepare_draft_area($secondimagedraftitemid, $secondimagefileinfo['contextid'],
                            $secondimagefileinfo['component'], $secondimagefileinfo['filearea'],
                            $secondimagefileinfo['itemid'],
                            $this->get_filemanager_options_array());
            $data['secondimage'] = $secondimagedraftitemid;

            // Get backpage text.
            if (!empty($data['secondpagetext'])) {
                $data['secondpagetext'] = array('text' => $data['secondpagetext'], 'format' => FORMAT_HTML);
            } else {
                $data['secondpagetext'] = array('text' => '', 'format' => FORMAT_HTML);
            }
        } else { // Load default.
            $data['certificatetext'] = array('text' => '', 'format' => FORMAT_HTML);
            $data['secondpagetext'] = array('text' => '', 'format' => FORMAT_HTML);
        }

        // Completion rules.
        $data['completiontimeenabled'] = !empty($data['requiredtime']) ? 1 : 0;

    }

    /**
     * Load in existing data as form defaults. Usually new entry defaults are stored directly in
     * form definition (new entry form); this function is used to load in data where values
     * already exist and data is being edited (edit entry form).
     *
     * @param mixed $default_values object or array of default values
     */
    function set_data($default_values) {
        if (is_object($default_values)) {
            $default_values = (array)$default_values;
        }

        $this->data_preprocessing($default_values);
        parent::set_data($default_values);
    }


    protected function get_filemanager_options_array () {
        global $COURSE;

        return [
            'subdirs' => 0,
            'maxbytes' => $COURSE->maxbytes,
            'maxfiles' => 1,
            'accepted_types' => ['image']
        ];
    }
}
