<?php

namespace local_legacy\certificate;

require_once($CFG->libdir . '/pdflib.php');

use local_legacy\models\certificate\certificate;
use local_legacy\models\certificate\certificate_issue;
use local_legacy\models\situacao_curso;
use pdf;

class certificate_renderer {

    protected certificate_issue $issue;
    
    public function __construct(certificate_issue $issue) {
        $this->issue = $issue;
    }

    public function fetch_tags_data() : object {

    }

    public function render() : pdf {

    }
}
