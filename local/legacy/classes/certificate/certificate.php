<?php namespace local_legacy\certificate;

use context_system;
use core\context;
use stdClass;

/**
 * A fork from mod_simplecertificate
 */
class certificate {

    const CERTIFICATE_COMPONENT_NAME = 'local_legacy';
    const CERTIFICATE_IMAGE_FILE_AREA = 'certificate_images';
    const CERTIFICATE_ISSUES_FILE_AREA = 'certificate_issues';

    const OUTPUT_OPEN_IN_BROWSER = 0;
    const OUTPUT_FORCE_DOWNLOAD = 1;
    const OUTPUT_SEND_EMAIL = 2;

    // Date Options Const.
    const CERT_ISSUE_DATE = -1;
    const COURSE_COMPLETATION_DATE = -2;
    const COURSE_START_DATE = -3;

    // Grade Option Const.
    const NO_GRADE = 0;
    const COURSE_GRADE = -1;

    // View const.
    const DEFAULT_VIEW = 0;
    const ISSUED_CERTIFCADES_VIEW = 1;
    const BULK_ISSUE_CERTIFCADES_VIEW = 2;

    // Pagination.
    const SIMPLECERT_MAX_PER_PAGE = 200;




    /**
     * Get the first page background image fileinfo
     *
     * @return array the first page background image fileinfo
     */
    public static function get_certificate_image_fileinfo() {
        return [
            'contextid' => context_system::instance()->id,
            'component' => self::CERTIFICATE_COMPONENT_NAME,
            'filearea' => self::CERTIFICATE_IMAGE_FILE_AREA,
            'itemid' => 1,
            'filepath' => '/'
        ];
    }

    /**
     * Get the second page background image fileinfo
     *
     * @return array the second page background image fileinfo
     */
    public static function get_certificate_secondimage_fileinfo() {
        $fileinfo = self::get_certificate_image_fileinfo();
        $fileinfo['itemid'] = 2;
        return $fileinfo;
    }

    /**
     * Get the temporary filearea, used to store user
     * profile photos to make the certiticate
     *
     * @return the temporary fileinfo
     */
    public static function get_certificate_tmp_fileinfo() {
        return [
            'contextid' => context_system::instance()->id,
            'component' => self::CERTIFICATE_COMPONENT_NAME,
            'filearea' => 'tmp',
            'itemid' => 0,
            'filepath' => '/'
        ];
    }

    public function get_context() : context {
        return context_system::instance();
    }

    /**
     *
     * @param stdClass $formdata The data submitted from the form
     * @return stdClass The certificate instance object
     */
    private function populate_certificate_instance(stdclass $formdata) {
        // Clear image filearea.
        $fs = get_file_storage();
        $fs->delete_area_files($this->get_context()->id, self::CERTIFICATE_COMPONENT_NAME, self::CERTIFICATE_IMAGE_FILE_AREA);

        // Creating a certificate instace object.
        $update = new stdClass();

        if (isset($formdata->certificatetext['text'])) {
            $update->certificatetext = $formdata->certificatetext['text'];
            if (!isset($formdata->certificatetextformat)) {
                $update->certificatetextformat = $formdata->certificatetext['format'];
            }
            unset($formdata->certificatetext);
        }

        if (isset($formdata->secondpagetext['text'])) {
            $update->secondpagetext = $formdata->secondpagetext['text'];
            if (!isset($formdata->secondpagetextformat)) {
                $update->secondpagetextformat = $formdata->secondpagetext['format'];
            }
            unset($formdata->secondpagetext);
        }

        if (isset($formdata->certificateimage)) {
            if (!empty($formdata->certificateimage)) {
                $fileinfo = self::get_certificate_image_fileinfo();
                $formdata->certificateimage = $this->save_upload_file($formdata->certificateimage, $fileinfo);
            }
        } else {
            $formdata->certificateimage = null;
        }

        if (isset($formdata->secondimage)) {
            if (!empty($formdata->secondimage)) {
                $fileinfo = self::get_certificate_secondimage_fileinfo();
                $formdata->secondimage = $this->save_upload_file($formdata->secondimage, $fileinfo);
            }
        } else {
            $formdata->secondimage = null;
        }

        foreach ($formdata as $name => $value) {
            $update->{$name} = $value;
        }

        if (isset($formdata->instance)) {
            $update->id = $formdata->instance;
            unset($update->instance);
        }

        return $update;
    }

    /**
     * Save upload files in $fileinfo array and return the filename
     *
     * @param string $formitemid Upload file form id
     * @param array $fileinfo The file info array, where to store uploaded file
     * @return string filename
     */
    private function save_upload_file($formitemid, array $fileinfo) {
        // Clear file area.
        if (empty($fileinfo['itemid'])) {
            $fileinfo['itemid'] = '';
        }

        $fs = get_file_storage();
        $fs->delete_area_files($fileinfo['contextid'], $fileinfo['component'], $fileinfo['filearea'], $fileinfo['itemid']);
        file_save_draft_area_files($formitemid, $fileinfo['contextid'], $fileinfo['component'], $fileinfo['filearea'],
                                $fileinfo['itemid']);
        // Get only files, not directories.
        $files = $fs->get_area_files($fileinfo['contextid'], $fileinfo['component'], $fileinfo['filearea'], $fileinfo['itemid'], '',
                                    false);
        $file = array_shift($files);
        return $file->get_filename();
    }

    /**
     * Add this instance to the database.
     *
     * @param stdClass $formdata The data submitted from the form
     * @param mod_simplecertificate_mod_form $mform the form object to get files
     * @return mixed false if an error occurs or the int id of the new instance
     */
    public function add_instance(stdClass $formdata) {
        global $DB;

        // Add the database record.
        $update = $this->populate_certificate_instance($formdata);
        $update->timecreated = time();
        $update->timemodified = $update->timecreated;

        $returnid = $DB->insert_record('simplecertificate', $update, true);

        return $returnid;
    }

    /**
     * Update this instance in the database.
     *
     * @param stdClass $formdata - the data submitted from the form
     * @return bool false if an error occurs
     */
    public function update_instance(stdClass $formdata) {
        global $DB;

        $update = $this->populate_simplecertificate_instance($formdata);
        $update->timemodified = time();

        $result = $DB->update_record('simplecertificate', $update);

        if (!$DB->execute(
                        'UPDATE {simplecertificate_issues} SET haschange = 1 WHERE timedeleted is NULL AND certificateid = :certid',
                        array('certid' => $this->get_instance()->id))) {
            print_error('cannotupdatemod', '', '', self::CERTIFICATE_COMPONENT_NAME,
                        'Error update simplecertificate, markig issues
                     with has change');
        }

        $this->instance = $DB->get_record('simplecertificate', array('id' => $update->id), '*', MUST_EXIST);
        if (!$this->instance) {
            print_error('certificatenot', 'simplecertificate');
        }

        return $result;
    }
}
