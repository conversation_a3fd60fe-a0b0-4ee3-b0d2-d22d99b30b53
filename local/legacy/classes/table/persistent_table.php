<?php
namespace local_legacy\table;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/tablelib.php');

use coding_exception;
use flexible_table;

/**
 * Class persistent_table
 *
 * A flexible_table subclass that renders data from a core\persistent class.
 * - Infers columns via static properties_definition().
 * - Allows omitting columns via method calls.
 * - Supports overriding headers per column via subclassing.
 */
class persistent_table extends flexible_table {
    /** @var string Fully-qualified persistent classname. */
    protected $persistentclass;

    /** @var array Filters passed to count_records() / get_records(). */
    protected $filters = [];

    /** @var string ORDER BY field; default to 'id'. */
    protected $sort = 'id';

    /** @var string Sort direction: 'ASC' or 'DESC'. */
    protected $order = 'ASC';

    /** @var string[] List of persistent property names. */
    protected $persistentcolumns = [];

    /**
     * Constructor.
     *
     * @param string $uniqueid        Unique table identifier (session key).
     * @param string $persistentclass Fully-qualified persistent class.
     * @throws coding_exception If the class doesn't exist or has no properties.
     */
    public function __construct(string $uniqueid, string $persistentclass) {
        parent::__construct($uniqueid);

        if (!class_exists($persistentclass)) {
            throw new coding_exception("Persistent class '{$persistentclass}' not found");
        }
        $this->persistentclass = $persistentclass;

        // Infer columns via static definition.
        $definitions = $persistentclass::properties_definition();
        if (empty($definitions)) {
            throw new coding_exception("Persistent class '{$persistentclass}' returned no properties_definition");
        }
        $this->persistentcolumns = array_keys($definitions);

        // Initialize table columns and headers.
        $this->define_columns($this->persistentcolumns);
        $this->define_headers($this->get_headers());

        // Default styling.
        $this->set_attribute('class', 'generaltable generalbox');
    }

    /**
     * Omit specified columns from display.
     *
     * @param string[] $omit List of column names to omit.
     */
    public function omit_columns(array $omit): void {
        $this->persistentcolumns = array_diff($this->persistentcolumns, $omit);
        $this->define_columns($this->persistentcolumns);
        $this->define_headers($this->get_headers());
    }

    /**
     * Override this to rename headers per column.
     *
     * @return string[] Map of column => header label.
     */
    protected function get_header_overrides(): array {
        return [];
    }

    /**
     * Default CSS class for rows. Override if needed.
     *
     * @param object $record Row object.
     * @return string CSS classes.
     */
    protected function get_row_class(object $record): string {
        return '';
    }

    /**
     * Build headers list based on persistent columns and overrides.
     *
     * @return string[] Array of header labels.
     */
    protected function get_headers(): array {
        $overrides = $this->get_header_overrides();
        $headers = [];
        foreach ($this->persistentcolumns as $col) {
            if (isset($overrides[$col])) {
                $headers[] = $overrides[$col];
            } else {
                $label = preg_replace('/(?<!^)[A-Z]/', ' $0', $col);
                $label = str_replace('_', ' ', $label);
                $headers[] = ucfirst($label);
            }
        }
        return $headers;
    }

    /**
     * Set filters for the persistent query.
     *
     * @param array $filters [field => value].
     */
    public function set_filters(array $filters): void {
        $this->filters = $filters;
    }

    /**
     * Set sorting parameters.
     *
     * @param string $sort  Field to sort by.
     * @param string $order 'ASC' or 'DESC'.
     */
    public function set_sort(string $sort, string $order = 'ASC'): void {
        $this->sort = $sort;
        $this->order = strtoupper($order) === 'DESC' ? 'DESC' : 'ASC';
    }

    /**
     * Query the persistent for data and populate rawdata.
     * Uses to_record() when exporting.
     *
     * @param int  $pagesize       Number of rows per page.
     * @param bool $useinitialsbar Ignored.
     */
    public function query_db(int $pagesize, bool $useinitialsbar = true): void {
        if (empty($this->persistentclass)) {
            throw new coding_exception('Persistent class not defined');
        }
        $class = $this->persistentclass;

        // Determine total and setup pagination.
        $total = $class::count_records($this->filters);
        $this->pagesize($pagesize, $total);

        // Fetch records.
        $offset = $this->get_page_start();
        $limit  = $this->get_page_size();
        $records = $class::get_records(
            $this->filters,
            "{$this->sort} {$this->order}",
            '',
            $offset,
            $limit
        );


        $formatted = [];
        foreach ($records as $record) {
            $formatted[] = (array)$record->to_record();
        }
        $this->rawdata = $formatted;
    }

    /**
     * Populate the table by iterating formatted rows directly.
     */
    public function build_table(): void {
        if (empty($this->rawdata) || !is_array($this->rawdata)) {
            return;
        }
        foreach ($this->rawdata as $row) {
            $rowdata = [];
            foreach ($this->persistentcolumns as $col) {
                $colmethod = 'col_' . $col;
                if (method_exists($this, $colmethod)) {
                    $rowdata[] = $this->$colmethod((object)$row);
                } else {
                    $value = $row[$col] ?? '';
                    $custom = $this->other_cols($col, (object)$row);
                    $rowdata[] = $custom !== null ? $custom : $value;
                }
            }
            $this->add_data($rowdata, $this->get_row_class((object)$row));
        }
    }

    /**
     * Combined convenience method: setup, query, build, finish.
     *
     * @param int    $pagesize            Rows per page.
     * @param bool   $useinitialsbar      Ignored.
     * @param string $downloadhelpbutton  Unused.
     */
    public function out(int $pagesize, bool $useinitialsbar = true, string $downloadhelpbutton = ''): void {
        $this->setup();
        $this->query_db($pagesize, $useinitialsbar);
        $this->build_table();
        $this->finish_output();
    }
}
