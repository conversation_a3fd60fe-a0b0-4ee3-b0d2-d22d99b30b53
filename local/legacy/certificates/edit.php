<?php

require(__DIR__ . '/../../../config.php');

require_capability('local/legacy:editcertificates', context_system::instance());

$id = optional_param('id', 0, PARAM_INT);
$url = new moodle_url('/local/legacy/certificates/edit.php', []);
$redirect_url = new moodle_url('local/legacy/certificates/index.php');

$PAGE->set_url($url);
$PAGE->set_context(context_system::instance());
$PAGE->set_title(get_string('pluginname', 'local_legacy'));
$PAGE->set_heading(get_string('pluginname', 'local_legacy'));

$form = new local_legacy\certificate\forms\edit_form();

if ($id) {
    // TODO: retrieve record from DB
    $record = new stdClass();
    $record->id = $id;
    // Example: $record->name = 'Certificado Exemplo';
    // ... preencha outros campos conforme necessário ...
    $form->set_data($record);
}

if ($form->is_cancelled()) {
    redirect($redirect_url);
} elseif ($data = $form->get_data()) {
    // TODO: salvar os dados ($data) na base de dados
    if (!empty($data->id)) {
        // Atualizar registro existente
    } else {
        // Criar novo registro
    }

    redirect($redirect_url, get_string('changessaved', 'local_legacy'));  
}

echo $OUTPUT->header();

// Display form
$form->display();

echo $OUTPUT->footer();
