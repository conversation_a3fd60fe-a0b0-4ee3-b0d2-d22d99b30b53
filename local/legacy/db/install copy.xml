<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/legacy/db" VERSION="20250522" COMMENT="XMLDB file for Moodle report/eadtech"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="legacy_certificate" COMMENT="EADTECH Certificates">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="learningsolutiontype" TYPE="char" LENGTH="8" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="width" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="297" SEQUENCE="false"/>
        <FIELD NAME="height" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="210" SEQUENCE="false"/>
        <FIELD NAME="certificateimage" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="certificatetext" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="certificatetextformat" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="certificatetextx" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="certificatetexty" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="50" SEQUENCE="false"/>
        <FIELD NAME="outcome" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="certdate" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="-2" SEQUENCE="false"/>
        <FIELD NAME="certdatefmt" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="certgrade" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="gradefmt" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="printqrcode" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false"/>
        <FIELD NAME="qrcodefirstpage" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="codex" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="codey" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="enablesecondpage" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="secondpagex" TYPE="int" LENGTH="4" NOTNULL="false" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="secondpagey" TYPE="int" LENGTH="4" NOTNULL="false" DEFAULT="50" SEQUENCE="false"/>
        <FIELD NAME="secondpagetext" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="secondpagetextformat" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="secondimage" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timestartdatefmt" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
    </TABLE>
    <TABLE NAME="legacy_certificate_issues" COMMENT="EADTECH Certificate issues">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="certificateid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="codsituacaoaluno" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="certificatename" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="code" TYPE="char" LENGTH="36" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="timedeleted" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="haschange" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="pathnamehash" TYPE="char" LENGTH="40" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="coursename" TYPE="char" LENGTH="255" NOTNULL="true" DEFAULT="--" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="certificate_user" UNIQUE="false" FIELDS="certificateid, userid"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_turmas_cursos" COMMENT="Turmas de cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nometurma" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="numeromaximoalunos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="numerominimoalunos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="disponivel" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="descricao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="cargahoraria" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
        <FIELD NAME="datainicioprematricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datafimprematricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="cancelada" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="true" FIELDS="codturma"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_turmas_trilhas" COMMENT="Turmas de trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Id"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ActivityCollectionId"/>
        <FIELD NAME="nometurma" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false" COMMENT="name"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Period_From"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Period_To"/>
        <FIELD NAME="numeromaximoalunos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="Vacancies"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="RegistrationDate"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="ModificationDate"/>
        <FIELD NAME="disponivel" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Active"/>
        <FIELD NAME="descricao" TYPE="text" NOTNULL="false" SEQUENCE="false" COMMENT="Description"/>
        <FIELD NAME="datainicioprematricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="PreSubscription_Period_From"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="true" FIELDS="codturma"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_situacao_curso" COMMENT="Situacao de alunos em cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datamatricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="percconclusao" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="percaproveitamento" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="prazodeacesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="origemmatricula" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="statusmatricula" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="progressstatus" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="data_de_cancelamento" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="motivo_do_cancelamento" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="nota_do_usuario" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="data_do_primeiro_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="data_do_ultimo_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="data_de_conclusao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="true" FIELDS="codturma"/>
        <INDEX NAME="codcurso_idx" UNIQUE="true" FIELDS="codcurso,codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_situacao_trilha" COMMENT="Situacao de alunos em trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="studentClass.ActivityCollectionId"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="studentClass.ActivityCollectionClassId"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datamatricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.RegistrationDate"/>
        <FIELD NAME="percconclusao" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2" COMMENT="studentClass.TrainingState_PercentConcluded"/>
        <FIELD NAME="percaproveitamento" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2" COMMENT="studentClass.TrainingState_PercentGrade"/>
        <FIELD NAME="prazodeacesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.Period_From"/>
        <FIELD NAME="origemmatricula" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false" COMMENT="studentClass.EnrollmentOrigin"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.Period_From"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.Period_To"/>
        <FIELD NAME="statusmatricula" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" COMMENT="studentClass.state"/>
        <FIELD NAME="progressstatus" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" COMMENT="Status de progresso"/>
        <FIELD NAME="nota_do_usuario" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2" COMMENT="studentClass.TrainingState_AccessedOn"/>
        <FIELD NAME="data_do_primeiro_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.TrainingState_FirstAccess"/>
        <FIELD NAME="data_do_ultimo_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.TrainingState_AccessedOn"/>
        <FIELD NAME="data_de_conclusao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.TrainingState_ConcludedOn"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="true" FIELDS="codturma"/>
        <INDEX NAME="codtrilha_idx" UNIQUE="true" FIELDS="codtrilha,codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_corpo_docente" COMMENT="Usuários do corpo docente">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nome" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="email" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="cargo" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="telefone" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="celular" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="cpf" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="uf" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="coordenador" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codcorpo_idx" UNIQUE="true" FIELDS="codcorpo"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_corpo_docente_curso" COMMENT="Relacionamento de corpo docente com turmas de cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="dataalocacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codcorpo_turma_idx" UNIQUE="true" FIELDS="codcorpo,codturma"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_corpo_docente_trilha" COMMENT="Relacionamento de corpo docente com turmas de trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="[dbo].LMS.TRN_ActivityCollectionClasses.ID"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="[dbo].LMS.TRN_Activities.Id"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codcorpo_turma_idx" UNIQUE="true" FIELDS="codcorpo,codturma"/>
      </INDEXES>
    </TABLE>
  </TABLES>
</XMLDB>
