<?php

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Repositório de dados legados (EADTECH)';
$string['legacy:editcertificates'] = 'Editar certificados legados';

// Certificate
$string['certificate:certificatename'] = 'Certificate Name';
$string['certificate:certificateimage'] = 'Certificate Image File';
$string['certificate:certificatetext'] = 'Certificate Text';
$string['certificate:certificatetextx'] = 'Certificate Text Horizontal Position';
$string['certificate:certificatetexty'] = 'Certificate Text Vertical Position';
$string['certificate:height'] = 'Certificate Height';
$string['certificate:width'] = 'Certificate Width';
$string['certificate:coursename'] = 'Alternative Course Name';
$string['certificate:intro'] = 'Introduction';
$string['certificate:printoutcome'] = 'Print Outcome';
$string['certificate:printdate'] = 'Print Date';

$string['certificate:secondpageoptions'] = 'Certificate Back page';
$string['certificate:enablesecondpage'] = 'Enable Certificate Back page';
$string['certificate:enablesecondpage_help'] = 'Enable Certificate Back page edition, if is disabled, only certificate QR code will be printed in back page (if the QR code is enabled)';
$string['certificate:secondimage'] = 'Certificate Back Image file';
$string['certificate:secondimage_help'] = 'This is the picture that will be used in the back of certificate';
$string['certificate:secondpagetext'] = 'Certificate Back Text';

$string['certificate:secondpagex'] = 'Certificate Back Text Horizontal Position';
$string['certificate:secondpagey'] = 'Certificate Back Text Vertical Position';
$string['certificate:secondtextposition'] = 'Certificate Back Text Position';
$string['certificate:secondtextposition_help'] = 'These are the XY coordinates (in millimeters) of the certificate back page text';

$string['certificate:printqrcode'] = 'Print Certificate QR Code';
$string['certificate:printqrcode_help'] = 'Print (or not) certificate QR Code';
$string['certificate:codex'] = 'Certificate QR Code Horizontal Position';
$string['certificate:codey'] = 'Certificate QR Code Vertical Position';
$string['certificate:qrcodeposition'] = 'Certificate QR Code Position';
$string['certificate:qrcodeposition_help'] = 'These are the XY coordinates (in millimeters) of the certificate QR Code';
$string['certificate:defaultcodex'] = 'Default Horizontal QR code Position';
$string['certificate:defaultcodey'] = 'Default Vertical QR code Position';

$string['certificate:issueddate'] = 'Date Issued';
$string['certificate:completiondate'] = 'Course Completion';
$string['certificate:coursestartdate'] = 'Course Start Date';
$string['certificate:datefmt'] = 'Date Format';
$string['certificate:userdateformat'] = 'User\'s Language Date Format';
$string['certificate:printgrade'] = 'Print Grade';
$string['certificate:gradefmt'] = 'Grade Format';

$string['certificate:gradeletter'] = 'Letter Grade';
$string['certificate:gradepercent'] = 'Percentage Grade';
$string['certificate:gradepoints'] = 'Points Grade';
$string['certificate:coursetimereq'] = 'Required minutes in course';
$string['certificate:emailteachers'] = 'Email Teachers';
$string['certificate:emailothers'] = 'Email Others';
$string['certificate:emailfrom'] = 'Email From name';
$string['certificate:delivery'] = 'Delivery';

$string['certificate:certificatename_help'] = 'Certificate Name';
$string['certificate:certificatetext_help'] = '<ul>
<li>{XXXX} -> XXXXX</li>
</ul>';
$string['certificate:textposition'] = 'Certificate Text Position';
$string['certificate:textposition_help'] = 'These are the XY coordinates (in millimetres) of the certificate text';
$string['certificate:size'] = 'Certificate Size';
$string['certificate:size_help'] = 'These are the Width and Height size (in millimetres) of the certificate, Default size is A4 Landscape';
$string['certificate:coursename_help'] = 'Alternative Course Name';
$string['certificate:certificateimage_help'] = 'This is the picture that will be used in the certificate';

$string['certificate:printoutcome_help'] = 'You can choose any course outcome to print the name of the outcome and the user\'s received outcome on the certificate.  An example might be: Assignment Outcome: Proficient.';
$string['certificate:printdate_help'] = 'This is the date that will be printed, if a print date is selected. If the course completion date is selected but the student has not completed the course, the date received will be printed. You can also choose to print the date based on when an activity was graded. If a certificate is issued before that activity is graded, the date received will be printed.';
$string['certificate:datefmt_help'] = 'Enter a valid PHP date format pattern (<a href="http://www.php.net/manual/en/function.strftime.php"> Date Formats</a>). Or, leave it empty to use the format of the user\'s chosen language.';
$string['certificate:printgrade_help'] = 'You can choose any available course grade items from the gradebook to print the user\'s grade received for that item on the certificate.  The grade items are listed in the order in which they appear in the gradebook. Choose the format of the grade below.';
$string['certificate:gradefmt_help'] = 'There are three available formats if you choose to print a grade on the certificate:
<ul>
<li>Percentage Grade: Prints the grade as a percentage.</li>
<li>Points Grade: Prints the point value of the grade.</li>
<li>Letter Grade: Prints the percentage grade as a letter.</li>
</ul>';
$string['certificate:qrcodefirstpage'] = 'Print QR Code in the first page';
$string['certificate:qrcodefirstpage_help'] = 'Print QR Code in the first page';
$string['certificate:timestartdatefmt'] = 'Enrollment start date format';
$string['certificate:timestartdatefmt_help'] = 'Enter a valid PHP date format pattern (<a href="http://www.php.net/manual/en/function.strftime.php"> Date Formats</a>). Or, leave it empty to use the format of the user\'s chosen language.';

$string['certificate:issueoptions'] = 'Issue Options';
$string['certificate:designoptions'] = 'Design Options';

$string['certificate:filenotfound'] = 'File not Found';
$string['certificate:invalidcode'] = 'Invalid certificate code';
$string['certificate:variablesoptions'] = 'Others Options';
$string['certificate:certificatetexterror'] = "Invalid certificate text";
$string['certificate:issued'] = 'Issued';
$string['certificate:coursegrade'] = 'Course Grade';