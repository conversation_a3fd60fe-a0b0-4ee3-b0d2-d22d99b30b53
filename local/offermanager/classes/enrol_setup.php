<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager;

use local_offermanager\constants;
use local_offermanager\persistent\offer_model;
use core_plugin_manager;
use core\notification;
use core\plugininfo\enrol;

defined('MOODLE_INTERNAL') || die();

/**
 * Class enrol_setup
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_setup
{
    const FIELDS_MAP = [
        'classname' => 'name',
        'ue_status' => 'status',
        'startdate' => 'customint3',
        'enddate' =>  'customint4',
        'enableenddate' => 'customint1',
        'enablepreenrolment' => 'customint2',
        'preenrolmentstartdate' => 'enrolstartdate',
        'preenrolmentenddate' => 'enrolenddate',
        'description' => 'customtext1',
        'maxusers' => 'customint5',
        'minusers' => 'customint6',
        'roleid' => 'roleid',
        'enableenrolperiod' => 'customint7',
        'enrolperiod' => 'enrolperiod',
        'enableextension' => 'customint8',
        'extensiondata' => 'customtext3',
        'enablereenrol' => 'customint9',
        'reenrolmentsituations' => 'customchar3',
        'teachers' => 'customtext2',
    ];

    /**
     * Desativa as ofertas.
     *
     * @return void
     * @throws dml_exception
     */
    public static function disable_offers(): void
    {
        $records = offer_model::get_records([
            'status' => constants::OFFER_STATUS_ACTIVE
        ]);

        foreach ($records as $record) {
            $record->inactivate();
        }
    }

    /**
     * Desativa o `defaultenrol` de todos os plugins de inscrição, exceto o `enrol_offer`.
     *
     * @return void
     * @throws dml_exception
     */
    public static function disable_defaultenrol_for_other_plugins(): void
    {
        $records = self::get_other_active_plugins();

        foreach ($records as $record) {
            set_config('defaultenrol', 0, $record->plugin);
        }
    }

    /**
     * Return the enrol plugins that depends on local_offermanager
     *
     * @param bool $enabled returns only enabled plugins
     *
     * @return string[]
     */
    public static function get_dependent_enrol_plugins($enabled = true)
    {
        $pluginmanager = core_plugin_manager::instance();
        $enrol_plugins = enrol_get_plugins($enabled);

        $filtered_plugins = [];

        foreach ($enrol_plugins as $name => $instance) {

            $plugin = $pluginmanager->get_plugin_info('enrol_' . $name);

            $required = $plugin->get_other_required_plugins();

            if (empty($required)) {
                continue;
            }

            foreach ($required as $component => $notused) {
                if ($component == 'local_offermanager') {
                    $filtered_plugins[] = $plugin->name;
                    break;
                }
            }
        }

        return $filtered_plugins;
    }

    /**
     * Retorna os plugins de inscrição que não são controlados pelo plugin
     * e que encontram-se ativos.
     *
     * @return array[object]
     * @throws dml_exception
     */
    public static function get_other_active_plugins(): array
    {
        global $DB;

        $dependent_plugins = self::get_dependent_enrol_plugins();
        
        if (empty($dependent_plugins)) {
            return [];
        }

        [$insql, $inparams] = $DB->get_in_or_equal(
            $dependent_plugins,
            SQL_PARAMS_QM,
            'param',
            false
        );

        return $DB->get_records_select(
            "config_plugins",
            "name = 'defaultenrol' AND plugin $insql",
            $inparams
        );
    }

    /**
     * Verifica se existem plugins de inscrição para serem desabilitados.
     *
     * @return bool
     * @throws dml_exception
     */
    public static function has_enrol_plugins_to_disable(): bool
    {
        global $DB;

        $dependent_plugins = self::get_dependent_enrol_plugins();
        
        if (empty($dependent_plugins)) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal(
            $dependent_plugins,
            SQL_PARAMS_QM,
            'param',
            false
        );

        return $DB->record_exists_select(
            'config_plugins',
            "name = 'defaultenrol' AND plugin $insql",
            $inparams
        );
    }

    public static function disable_enrol_plugins()
    {
        $enabled = enrol_get_plugins(true);

        $enrol_plugins = self::get_dependent_enrol_plugins();

        foreach ($enabled as $pluginname => $enrol_plugin) {
            $fullname = "{$pluginname}";

            if (!in_array($fullname, $enrol_plugins)) {
                enrol::enable_plugin($pluginname, false);

                notification::add(
                    get_string('plugin_disabled', 'core_admin', $fullname),
                    notification::SUCCESS
                );
            }
        }
    }

    public static function handle_enrol_plugins()
    {
        global $CFG;

        require_once("{$CFG->dirroot}/local/offermanager/classes/enrol_setup.php");

        if (
            get_config('local_offermanager', 'enableplugin')
            && !get_config('local_offermanager', 'enableplatformenrol')
            && self::has_enrol_plugins_to_disable()
        ) {
            self::disable_enrol_plugins();
        }

        return false;
    }

    public static function handle_disable_plugins()
    {
        self::disable_offers();
        self::handle_enrol_plugins();
    }

    public static function get_default_extension_data()
    {
        return json_encode([
            'period' => 0,
            'days_available' => 0,
            'max_requests' => 0,
            'allowed_situations' => null
        ]);
    }
}
