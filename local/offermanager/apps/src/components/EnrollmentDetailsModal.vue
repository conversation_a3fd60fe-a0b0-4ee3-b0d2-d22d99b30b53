<template>
  <div v-if="show" class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">Informações da matrícula</h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body" v-if="user">
        <div class="details-container">
          <div class="detail-row">
            <div class="detail-label">Nome completo</div>
            <div class="detail-value">{{ user.fullName }}</div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Curso</div>
            <div class="detail-value">{{ courseName }}</div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Método de inscrição</div>
            <div class="detail-value">
              {{ getEnrolmentMethod(user.enrol) }}
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Estado</div>
            <div class="detail-value">
              <span
                class="state-tag"
                :class="user.state === 0 ? 'state-ativo' : 'state-inativo'"
              >
                {{ user.stateName }}
              </span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Matrícula criada</div>
            <div class="detail-value">{{ user.createdDate }}</div>
          </div>
        </div>
      </div>
      <div v-else class="modal-body no-data">Nenhum dado disponível</div>

      <!-- Rodapé do modal -->
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="$emit('close')">
          Cancelar
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EnrollmentDetailsModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: null,
    },
    courseName: {
      type: String,
      default: "",
    },
  },
  emits: ["close"],
  methods: {
    getEnrolmentMethod(enrol) {
      console.log(
        "EnrollmentDetailsModal - Método de inscrição recebido:",
        enrol
      );

      if (!enrol) return "Não disponível";

      switch (enrol) {
        case "offer_manual":
          return "Inscrição manual";
        case "offer_self":
          return "Autoinscrição";
        default:
          return enrol;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 600px;
  border: 1px solid #373b3e;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #373b3e;
  background-color: #212529;

  .modal-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: bold;
    color: #fff;
  }

  .modal-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;

    &:hover {
      color: #0b5ed7;
    }
  }
}

.modal-body {
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem 1rem;
  border-top: 1px solid #373b3e;
  background-color: #212529;

  .btn {
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .btn-secondary {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;

    &:hover:not(:disabled) {
      background-color: #6c757d;
      border-color: #6c757d;
    }
  }
}

.details-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.detail-row {
  display: flex;
  border-bottom: 1px solid #2c3034;

  &:last-child {
    border-bottom: none;
  }
}

.detail-label {
  width: 40%;
  padding: 12px 15px;
  font-weight: 500;
  color: #ffffff;
  font-size: 0.9rem;
  background-color: #1e2124;
}

.detail-value {
  width: 60%;
  padding: 12px 15px;
  color: #e6e6e6;
  font-size: 0.9rem;
  align-items: center;
}

.enrollment-icon {
  margin-left: 8px;
  color: var(--primary);
}

.state-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  text-align: center;
}

.state-ativo {
  background-color: #28a745;
  color: #ffffff;
}

.state-inativo {
  background-color: #dc3545;
  color: #ffffff;
}

.no-data {
  padding: 1rem;
  text-align: center;
  color: #adb5bd;
  font-style: italic;
}
</style>
