<template>
  <div v-if="show" class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">Remoção de Matrículas</h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body">
        <div class="enrollment-form">
          <div class="table-container">
            <CustomTable :headers="tableHeaders" :items="filteredUsers" />
          </div>

          <Pagination
            v-if="users.length > 0"
            v-show="users.length > perPage"
            v-model:current-page="currentPage"
            v-model:per-page="perPage"
            :total="users.length"
          />
        </div>
        <div class="text-center mt-5">
          <h5 class="mt-1">
            Tem certeza de que deseja excluir essas inscrições de usuário?
          </h5>
        </div>
      </div>

      <!-- Rodapé do modal -->
      <div class="modal-footer">
        <div class="footer-buttons">
          <button
            class="btn btn-primary"
            @click="$emit('confirm')"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? "Removendo..." : "Remover matrículas" }}
          </button>
          <button class="btn btn-secondary" @click="$emit('close')">
            Cancelar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "./Pagination.vue";
import CustomSelect from "./CustomSelect.vue";
import CustomTable from "./CustomTable.vue";

export default {
  name: "BulkDeleteEnrollmentModal",
  components: {
    Pagination,
    CustomSelect,
    CustomTable,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    users: {
      type: Array,
      default: () => [],
    },
    offerclassid: {
      type: [Number, String],
      required: true,
    },
  },
  emits: ["close", "confirm", "error"],
  data() {
    return {
      isSubmitting: false,

      currentPage: 1,
      perPage: 5,
      sortBy: "fullName",
      sortDesc: false,

      tableHeaders: [
        { text: "NOME/SOBRENOME", value: "fullName", sortable: false },
        { text: "ESTADO ", value: "stateName", sortable: false },
        {
          text: "INÍCIO DA MATRÍCULA",
          value: "startDate",
          sortable: false,
        },
        { text: "FIM DA MATRÍCULA", value: "endDate", sortable: false },
      ],
    };
  },
  computed: {
    filteredUsers() {
      // Aplicar ordenação
      const sortedCourses = [...this.users].sort((a, b) => {
        const modifier = this.sortDesc ? -1 : 1;
        if (a[this.sortBy] < b[this.sortBy]) return -1 * modifier;
        if (a[this.sortBy] > b[this.sortBy]) return 1 * modifier;
        return 0;
      });

      // Aplicar paginação
      const startIndex = (this.currentPage - 1) * this.perPage;
      const endIndex = startIndex + this.perPage;
      return sortedCourses.slice(startIndex, endIndex);
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 800px;
  border: 1px solid #373b3e;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #373b3e;
  background-color: #212529;
  position: relative;

  .modal-title {
    margin: 0;
    font-size: 18.75px;
    font-weight: bold;
    color: #f8f9fa;
  }

  .modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);

    i {
      font-size: 0.9rem;
    }

    &:hover {
      color: #ffffff;
      opacity: 0.8;
    }
  }
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;

  .table-container {
    margin-bottom: 0;
  }
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-top: 1px solid #373b3e;
  background-color: #212529;

  .footer-spacer {
    flex: 1;
  }

  .footer-buttons {
    display: flex;
    gap: 10px;
  }

  .btn {
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .btn-primary {
    font-size: 15px;

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  .btn-secondary {
    color: #000;
  }
}

.enrollment-form {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #f8f9fa;
  font-size: 15px;
  min-width: 220px;
  flex-shrink: 0;
}

.form-value {
  color: #f8f9fa;
  font-size: 15px;
  flex: 1;
}

.form-field {
  flex: 1;

  /* Sobrescrever o estilo global dos selects */
  :deep(.custom-select) {
    width: auto !important;
  }
}

.date-time-field {
  display: flex;
  gap: 10px;
  align-items: center;
}

.date-field {
  position: relative;
  flex: 2;

  input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    background-color: #212529;
    border: 1px solid #373b3e;
    border-radius: 4px;
    color: #fff;
    font-size: 0.875rem;
  }

  /* Estilo para o input de data */
  input[type="date"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px; /* Espaço para o ícone de calendário nativo */
    position: relative;
    cursor: pointer;
  }

  /* Estilo para o ícone de calendário nativo */
  input[type="date"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    color: var(--primary);
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
  }
}

.time-field {
  position: relative;
  flex: 1;

  input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    background-color: #212529;
    border: 1px solid #373b3e;
    border-radius: 4px;
    color: #fff;
    font-size: 0.875rem;
  }

  /* Estilo para o input de hora */
  input[type="time"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px; /* Espaço para o ícone de relógio nativo */
    position: relative;
    cursor: pointer;
  }

  /* Estilo para o ícone de relógio nativo */
  input[type="time"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    color: var(--primary);
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
  }
}

.enable-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  width: 100px;

  input[type="checkbox"] {
    margin: 0;
  }

  label {
    color: #fff;
    font-size: 0.875rem;
    margin: 0;
  }
}

.custom-checkbox {
  width: 20px;
  height: 20px;
  background-color: #212529;
  border: 1px solid #373b3e;
  border-radius: 3px;
  appearance: none;
  cursor: pointer;
  position: relative;

  &:checked {
    background-color: var(--primary);
    border-color: var(--primary);

    &::after {
      content: "";
      position: absolute;
      left: 7px;
      top: 3px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }
}

/* Wrapper para os selects */
.select-wrapper {
  width: 120px !important;
  position: relative !important;
  display: inline-block !important;

  :deep(.custom-select) {
    width: 120px !important;
    max-width: 120px !important;
  }

  :deep(.select-container) {
    width: 120px !important;
    max-width: 120px !important;
    height: 28px !important;
    overflow: hidden !important;
    position: relative !important;
  }

  :deep(.select-value) {
    max-width: 90px !important; /* Deixa espaço para a setinha */
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    display: inline-block !important;
  }

  :deep(.select-arrow) {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 16px !important;
    height: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 2 !important;
  }

  /* Sobrescrever o estilo global que pode estar causando o problema */
  :deep([data-v-944e90b]) {
    width: 120px !important;
  }
}

/* Estilo para selects menores */
.smaller-select {
  :deep(.select-container) {
    height: 28px !important;
    font-size: 12px !important;
    background-color: #212529 !important;
    border: 1px solid #373b3e !important;
    border-radius: 4px !important;
    color: #fff !important;
  }

  :deep(.select-value) {
    padding: 0 6px !important;
    font-size: 12px !important;
  }

  :deep(.select-dropdown) {
    font-size: 12px !important;
    max-width: 120px !important;
    width: 120px !important;
    background-color: #212529 !important;
    border: 1px solid #373b3e !important;
  }

  :deep(.select-option) {
    padding: 5px 6px !important;
    color: #fff !important;

    &:hover,
    &.selected {
      background-color: var(--primary) !important;
    }
  }
}

/* Estilo para campos desabilitados */
.disabled-field {
  opacity: 0.6;
  pointer-events: none;
}

/* Estilo para desabilitar apenas os inputs, não o checkbox */
.disabled-inputs-only {
  .date-field,
  .time-field {
    opacity: 0.6;
    pointer-events: none;
  }

  .enable-checkbox {
    opacity: 1;
    pointer-events: auto;
  }
}
</style>
