<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file export_potential_users
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require('../../config.php');

use local_offermanager\util\csv_export_writer;

require_login();

$offerclassid = required_param('offerclassid', PARAM_INT);

$url = new moodle_url(
    '/local/offermanager/export_potential_users.php',
    [
        'offerclassid' => $offerclassid
    ]
);

$PAGE->set_url($url);
$context = context_system::instance();
$PAGE->set_context($context);

$PAGE->set_heading($SITE->fullname);

function mask_username($username)
{
    if (strlen($username) <= 5) {
        return str_repeat('*', strlen($username));
    }
    return substr($username, 0, 3) . str_repeat('*', strlen($username) - 5) . substr($username, -2);
}

function mask_email($email)
{
    list($localpart, $domain) = explode('@', $email);
    if (strlen($localpart) <= 3) {
        $maskedlocalpart = str_repeat('*', strlen($localpart));
    } else {
        $maskedlocalpart = substr($localpart, 0, 3) . str_repeat('*', strlen($localpart) - 3);
    }
    return $maskedlocalpart . '@' . $domain;
}

$offerclassid = required_param('offerclassid', PARAM_INT);

if (!has_capability('local/offermanager:manage', $context)) {
    throw new moodle_exception('nopermissions', 'local_offermanager');
}

$offerclass = local_offermanager\persistent\offer_class_model::get_record(['id' => $offerclassid]);
$offercourse = $offerclass->get_offer_course();
$users = $offercourse->get_potential_users_to_enrol();

$csvexport = new csv_export_writer();
$csvexport->set_filename('usuarios_matricula_' . gmdate("Ymd_Hi"));

$csvexport->add_data(array(
    'userid',
    'firstname',
    'lastname',
    'username',
    'email'
));

foreach ($users as $user) {
    $csvexport->add_data(array(
        $user->id,
        $user->firstname,
        $user->lastname,
        mask_username($user->username),
        mask_email($user->email)
    ));
}

$csvexport->download_file();
exit;
