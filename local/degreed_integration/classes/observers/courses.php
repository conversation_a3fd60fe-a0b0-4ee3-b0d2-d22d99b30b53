<?php namespace local_degreed_integration\observers;

use \core\event\course_created;
use \core\event\course_updated;
use \core\event\course_deleted;

use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\task\process_course_change;
abstract class courses{

    public static function course_created(course_created $event){
        global $DB;
        try {
            process_course_change::create_and_enqueue($event->objectid);
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER, $th->getTrace());
        }
    }

    public static function course_updated(course_updated $event){
        try {
            process_course_change::create_and_enqueue($event->objectid);
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER, $th->getTrace());
        }
    }

    public static function course_deleted(course_deleted $event){
        try {
            $record = course_sync_record::get_or_create_from_courseid($event->objectid);
            $record->set('enabled', false);
            $record->mark_as_changed();
            $record->save();
            
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER, $th->getTrace());
        }
    }
}