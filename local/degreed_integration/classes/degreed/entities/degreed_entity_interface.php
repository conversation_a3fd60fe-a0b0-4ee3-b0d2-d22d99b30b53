<?php namespace local_degreed_integration\degreed\entities;

use \local_degreed_integration\models\entity_sync_record;

interface degreed_entity_interface {
    /**
     * Converts this class into a JSON that can
     * be sent to the Degreed API.
     *
     * @return string
     */
    public function to_api() : string;

    /**
     * Instanciates a new object from the API response (JSON)
     *
     * @param string $response
     * @return static
     */
    public static function from_api(string $response) : static;

}
