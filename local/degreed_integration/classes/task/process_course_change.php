<?php namespace local_degreed_integration\task;

use \local_degreed_integration\models\course_sync_record;

/**
 * Calls the judy's API to fetch information
 * about the API token's owner.
 */
class process_course_change extends \core\task\adhoc_task {

    /**
     * Execute the task.
     */
    public function execute() {
        $data = $this->get_custom_data();
        // course_sync_record::upsert_from_courseid($data->courseid);
        course_sync_record::upsert_from_courseid($data->courseid);
    }


    /**
     * Tries to execute this task. If it fails, it will be
     * enqueued for later
     *
     * @return void
     */
    public static function create_and_enqueue(int $courseid){
        $task = new static();
        $task->set_custom_data(['courseid' => $courseid]);
        \core\task\manager::queue_adhoc_task($task, true);
    }
}