<?php

require('../../config.php');
require_login();

if (!get_config('local_recertification', 'enablerecertification')) {
    redirect($CFG->wwwroot . '/', get_string('notenabled', 'local_recertification'), null, \core\output\notification::NOTIFY_ERROR);
}

require_capability('local/recertification:manage', context_system::instance());

$certificates = required_param('certificates', PARAM_TEXT);
$certificates = explode(',', $certificates);

list($insql, $inparams) = $DB->get_in_or_equal($certificates);

$files = $DB->get_records_sql_menu(
    "SELECT id,fileurl
    FROM {local_recertification_sc}
    WHERE id {$insql}
    ",
    $inparams
);

if (count($files) > 1) {

    $zip = new ZipArchive;
    $zipname = 'certificados.zip';

    if (file_exists($zipname)) {
        unlink($zipname);
    }

    if ($zip->open($zipname, ZipArchive::CREATE) === TRUE) {

        foreach ($files as $file) {

            $filepath = "{$CFG->dataroot}/{$file}";
            $filename = explode('/', $file);
            $filename = array_pop($filename);

            if (file_exists($filepath)) {
                $zip->addFile($filepath, $filename);
            }
        }

        $zip->close();

        header('Content-Type: application/zip');
        header('Content-disposition: attachment; filename=' . $zipname);
        header('Content-Length: ' . filesize($zipname));

        ob_clean();
        flush();
        readfile($zipname);

        unlink($zipname);

        exit;
    }
} else {

    $files = reset($files);
    $filename = explode('/', $files);
    $filename = array_pop($filename);
    $filepath = "{$CFG->dataroot}/{$files}";
    if (file_exists($filepath)) {

        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header("Content-Type: application/force-download");
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . urlencode($filename) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filepath));

        ob_clean();
        flush();
        readfile($filepath);

        exit;
    }
}
