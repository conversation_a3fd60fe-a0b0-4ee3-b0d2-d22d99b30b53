<?php

use local_recertification\output\renderer as renderer;

define('LOCAL_RECERTIFICATION_NOTHING', 0);
define('LOCAL_RECERTIFICATION_DELETE', 1);
define('LOCAL_RECERTIFICATION_EXTRAATTEMPT', 2);

function local_recertification_after_config()
{
    global $CFG, $DB, $PAGE, $OUTPUT;

    if (strpos($_SERVER['SCRIPT_FILENAME'], str_replace('\\', '/', $CFG->dirroot)) === 0) {
        $dirroot = str_replace('\\', '/', $CFG->dirroot);
    } else {
        $dirroot = $_SERVER['DOCUMENT_ROOT'];
    }

    $route = $_SERVER['SCRIPT_FILENAME'] ?: '';
    $route = str_replace($dirroot, '', $route);

    // Checking routes
    if (strpos($route, '/mod/simplecertificate/verify.php') !== false) {

        $code = optional_param('code', '', PARAM_RAW);

        $certificate = [];

        if ($code) {

            $certificate = $DB->get_record('local_recertification_sc', ['code' => $code]);
        }

        if ($certificate) {

            $params = [
                'username' => $DB->get_field_sql(
                    "SELECT CONCAT(firstname,' ',lastname)
                    FROM {user}
                    WHERE id = :userid
                    LIMIT 0,1
                    ",
                    ['userid' => $certificate->userid]
                ) ?: '',
                'coursename' => $certificate->coursename ?: '',
                'emissiondate' => $certificate->emissiontime ? date('d/m/Y h:i', $certificate->emissiontime) : '',
                'code' => $certificate->code ?: ''
            ];
            $PAGE->set_url($CFG->wwwroot . '/local/recertification/validation.php?code=' . $code);
            $PAGE->set_context(context_system::instance());
            $renderer = $PAGE->get_renderer('local_recertification');

            echo $OUTPUT->header();
            $renderer->get_validation_page($params);
            echo $OUTPUT->footer();
            exit;
        }
    }

    return;
}

function local_recertification_add_menubar_icon(){
	global $PAGE;
	
	if(!get_config("local_recertification", "enablerecertification") || !has_capability('local/recertification:manage', \context_system::instance()) || isguestuser()){
		return false;
	}
	
	return (object)[
		"name" => get_string('pluginname', 'local_recertification'),
		"icon" => 'icon-recerts',
		"url" => new \moodle_url("/local/recertification/index.php"),
		"active" => $PAGE->pagetype == "local-recertification" ? 'active' : '',
		"order" => 6
	];
}