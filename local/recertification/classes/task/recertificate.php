<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Used to check for users that need to recomple.
 *
 * @package    local_recertification
 * <AUTHOR>
 * @copyright  2017 Dan <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_recertification\task;

use local_recertification\task\update_queue;
use local_recertification\task\trait\reset_methods_trait;
use context_course;


defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/enrollib.php');

/**
 * Check for users that need to recertification.
 *
 * @package    local_recertification
 * <AUTHOR>
 * @copyright  2017 Dan Marsden
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class recertificate extends \core\task\scheduled_task
{
    use reset_methods_trait;
    /**
     * Returns the name of this task.
     */
    public function get_name()
    {
        // Shown in admin screens.
        return get_string('recertificate', 'local_recertification');
    }

    /**
     * Execute task.
     */
    public function execute()
    {
        global $CFG, $DB;

        if (!get_config('local_recertification', 'enablerecertification')) {
            return true;
        }

        require_once($CFG->dirroot . '/course/lib.php');
        require_once($CFG->dirroot . '/local/recertification/lib.php');
        require_once($CFG->libdir . '/completionlib.php');
        require_once($CFG->libdir . '/gradelib.php');
        require_once($CFG->dirroot . '/mod/assign/locallib.php');
        require_once($CFG->dirroot . '/mod/quiz/lib.php');
        require_once($CFG->dirroot . '/mod/simplecertificate/locallib.php');
        require_once($CFG->dirroot . '/mod/scorm/lib.php');

        mtrace('Iniciando recertificações...');
        $this->recertificate();
        mtrace('Recertificações concluídas');

        mtrace('Enviando pré emails...');
        $this->send_preemail();
        mtrace('Envio de pré emails concluído');

        mtrace('Enviando pós emails...');
        $this->send_postemail();
        mtrace('Envio de ós emails concluído');

        mtrace('Limpando fila de recertificação...');
        $this->remove_completed_recertification_from_queue();
        mtrace('Fila limpa!');

        return true;
    }


    private function recertificate()
    {
        global $DB, $CFG;

        $queue = $DB->get_records_sql(
            " SELECT *
            FROM {local_recertification_queue} lrq
            WHERE recertificate = 0
            AND recertification_schedule <= UNIX_TIMESTAMP()
            "
        );

        if (!$queue) {
            mtrace('Nenhuma recertificação para ser realizada.');
            return true;
        }

        $instances = array_unique(array_column($queue, 'instanceid'));

        list($insql, $params) = $DB->get_in_or_equal($instances);

        $instances = $DB->get_records_sql(
            "SELECT *
            FROM {local_recertification_inst}
            where id {$insql}
            ",
            $params
        );

        foreach ($instances as $key => $instance) {

            $instances[$key] = update_queue::get_instance_config($instance);
        }

        foreach ($queue as $item) {

            if (!isset($instances[$item->instanceid])) {

                continue;
            }

            $error = $this->reset_user($item->userid, $item->courseid);

            if (!$error) {
                $item->recertificate = 1;
                $item->recertification_complete = time();

                if ($item->sendpostemail == 1 && $item->postemail_schedule > 0) {
                    $item->postemail_schedule = $item->recertification_complete + ($item->postemail_schedule - $item->recertification_schedule);
                }

                $DB->update_record('local_recertification_queue', $item);
            } else {
                mtrace(date('Y-m-d h:i:s') . ' - ' . $error);
            }
        }

        // Difficult to find affected users, just purge all completion cache.
        \cache::make('core', 'completion')->purge();
        // Clear coursecompletion cache which was added in Moodle 3.2.
        \cache::make('core', 'coursecompletion')->purge();

        return true;
    }

    /**
     * Reset user completion.
     * @param \int $userid - id of user.
     * @param \stdClass $course - course record.
     * @param \stdClass $config - recertification config.
     */
    public function reset_user($userid, $courseid)
    {
        global $USER;

        $USER->id = 2;

        $error = $this->reset_user_data($userid, $courseid);

        $this->reenroll_user_in_course($userid, $courseid);

        $context = context_course::instance($courseid);

        $event = \local_recertification\event\recertification_reset::create(
            array(
                'objectid'      => $courseid,
                'relateduserid' => $userid,
                'courseid' => $courseid,
                'context' => $context,
            )
        );
        $event->trigger();

        return $error;
    }

    public function reset_user_data($userid, $courseid)
    {
        // check if has certificate, put it on history and delete instance
        $this->reset_simplecertificate($userid, $courseid);

        // Archive and delete course completion.
        $this->reset_completions($userid, $courseid);

        // Archive and delete specific activity data.
        $this->reset_quiz($userid, $courseid);
        $this->reset_scorm($userid, $courseid);
        $this->reset_survey($userid, $courseid);
        $error = $this->reset_assign($userid, $courseid);
        $this->reset_forum($userid, $courseid);
        $this->reset_feedback($userid, $courseid);
        $this->reset_supervideo($userid, $courseid);
        $this->reset_attendance($userid, $courseid);
        $this->reset_nps($userid,$courseid);

        // Delete current grade information.
        if (get_config('local_recertification', 'deletegradedata')) {
            $this->delete_grades($userid, $courseid);
        }

        return $error;
    }

    /**
     * Re-enroll user in the course after resetting data.
     * @param int $userid User ID.
     * @param int $courseid Course ID.
     */
    protected function reenroll_user_in_course($userid, $courseid)
    {
        global $DB;

        $ue = $DB->get_record_sql(
            " SELECT ue.timestart, ue.timeend, ra.roleid, e.enrol
            FROM {user_enrolments} ue
            JOIN {enrol} e ON (e.id = ue.enrolid)
            LEFT JOIN {context} c ON (c.instanceid = e.courseid AND c.contextlevel = 50)
            LEFT JOIN {role_assignments} ra ON (ra.userid = ue.userid AND ra.contextid = c.id)
            WHERE e.courseid = :courseid
            AND ue.userid = :userid
            LIMIT 0,1
            ",
            ['courseid' => $courseid, 'userid' => $userid]
        );

        if (!$ue) {
            return;
        }

        $enrol = enrol_get_plugin($ue->enrol);
        $enrolinstances = enrol_get_instances($courseid, true);

        if ($enrol && $enrolinstances) {

            $instance = null;

            foreach ($enrolinstances as $courseenrolinstance) {
                if ($courseenrolinstance->enrol == $ue->enrol) {
                    $instance = $courseenrolinstance;
                    break;
                }
            }

            if ($instance) {

                $enrolment = [
                    'userid' => $userid,
                    'courseid' => $courseid,
                    'roleid' => $ue->roleid,
                    'status' => ENROL_USER_ACTIVE
                ];

                if (method_exists($enrol, 'unenrol_user') && method_exists($enrol, 'enrol_user')) {

                    $enrol->unenrol_user($instance, $enrolment['userid']);

                    if ($ue->timestart) {

                        $enrolment['timestart'] = time();

                        if ($ue->timeend) {

                            $enrolment['timeend'] = $enrolment['timestart'] + ($ue->timeend - $ue->timestart);
                        } else {
                            $enrolment['timeend'] = 0;
                        }
                    } else {
                        $enrolment['timestart'] = time();
                        $enrolment['timeend'] = 0;
                    }

                    $enrol->enrol_user(
                        $instance,
                        $enrolment['userid'],
                        $enrolment['roleid'],
                        $enrolment['timestart'],
                        $enrolment['timeend'],
                        $enrolment['status']
                    );
                }
            }
        }
    }

    private function send_preemail()
    {

        global $DB;

        $queue = $DB->get_records_sql(
            " SELECT *
            FROM {local_recertification_queue} lrq
            WHERE sendpreemail = 1
            AND preemail_schedule <= UNIX_TIMESTAMP()
            "
        );

        if (count($queue) === 0) {
            mtrace('Nenhum email antes da recertificação para ser enviado.');
            return true;
        }

        $instances = array_unique(array_column($queue, 'instanceid'));

        list($insql, $params) = $DB->get_in_or_equal($instances);

        $instances = $DB->get_records_sql(
            "SELECT *
            FROM {local_recertification_inst}
            where id {$insql}
            ",
            $params
        );

        foreach ($instances as $key => $instance) {

            $instances[$key] = update_queue::get_instance_config($instance);
        }
        foreach ($queue as $item) {

            if (!isset($instances[$item->instanceid])) {
                $item->sendpostemail = 0;
                $item->postemail_complete = time();

                $DB->update_record('local_recertification_queue', $item);
                continue;
            }

            $sent_email = [];

            $roles = $instances[$item->instanceid]->customparams->preemailroles;

            if ($roles) {

                if (in_array(5, $roles)) {
                    $sent_email[] = $this->notify_user($item->userid, $instances[$item->instanceid], 'preemail');
                }

                $key = array_search(5, $roles);

                if ($key !== false) {
                    unset($roles[$key]);
                }

                if ($roles) {

                    foreach ($roles as $role) {
                        $roleusers = $DB->get_fieldset_sql(
                            "SELECT DISTINCT ue.userid
                            FROM {user_enrolments} ue
                            JOIN {enrol} e ON (e.id = ue.enrolid)
                            LEFT JOIN {context} c ON (c.instanceid = e.courseid AND c.contextlevel = 50)
                            LEFT JOIN {role_assignments} ra ON (ra.userid = ue.userid AND ra.contextid = c.id)
                            WHERE ra.roleid = :roleid
                             AND e.courseid = :courseid
                            ",
                            ['roleid' => $role, 'courseid' => $item->courseid]
                        );

                        if ($roleusers) {
                            $sent_email[] = $this->notify_user($item->userid, $instances[$item->instanceid], 'preemail', $roleusers);
                        }
                    }
                }
            }

            if (array_search(true, $sent_email) !== false) {
                $item->sendpreemail = 2;
                $item->preemail_complete = time();

                $DB->update_record('local_recertification_queue', $item);
            }
        }
    }

    private function send_postemail()
    {

        global $DB;

        $queue = $DB->get_records_sql(
            " SELECT *
            FROM {local_recertification_queue} lrq
            WHERE sendpostemail = 1
            AND postemail_schedule <= UNIX_TIMESTAMP()
            "
        );

        if (!$queue) {
            mtrace('Nenhum email após a recertificação para ser enviado.');
            return true;
        }

        $instances = array_unique(array_column($queue, 'instanceid'));

        list($insql, $params) = $DB->get_in_or_equal($instances);

        $instances = $DB->get_records_sql(
            "SELECT *
            FROM {local_recertification_inst}
            where id {$insql}
            ",
            $params
        );

        foreach ($instances as $key => $instance) {

            $instances[$key] = update_queue::get_instance_config($instance);
        }
        foreach ($queue as $item) {

            if (!isset($instances[$item->instanceid])) {
                $item->sendpostemail = 0;
                $item->postemail_complete = time();

                $DB->update_record('local_recertification_queue', $item);
                continue;
            }

            $sent_email = [];

            $roles = $instances[$item->instanceid]->customparams->postemailroles;

            if ($roles) {

                if (in_array(5, $roles)) {
                    $sent_email[] = $this->notify_user($item->userid, $instances[$item->instanceid], 'postemail');
                }

                $key = array_search(5, $roles);

                if ($key !== false) {
                    unset($roles[$key]);
                }

                if ($roles) {

                    foreach ($roles as $role) {
                        $roleusers = $DB->get_fieldset_sql(
                            "SELECT DISTINCT ue.userid
                            FROM {user_enrolments} ue
                            JOIN {enrol} e ON (e.id = ue.enrolid)
                            LEFT JOIN {context} c ON (c.instanceid = e.courseid AND c.contextlevel = 50)
                            LEFT JOIN {role_assignments} ra ON (ra.userid = ue.userid AND ra.contextid = c.id)
                            WHERE ra.roleid = :roleid
                             AND e.courseid = :courseid
                            ",
                            ['roleid' => $role, 'courseid' => $item->courseid]
                        );

                        if ($roleusers) {
                            $sent_email[] = $this->notify_user($item->userid, $instances[$item->instanceid], 'postemail', $roleusers);
                        }
                    }
                }
            }

            if (array_search(true, $sent_email) !== false) {
                $item->sendpostemail = 2;
                $item->postemail_complete = time();

                $DB->update_record('local_recertification_queue', $item);
            }
        }
    }


    /**
     * Notify user before recertification.
     * @param \int $userid - user id
     * @param \stdclass $course - record from course table.
     * @param \stdClass $config - recertification config.
     */
    protected function notify_user($userid, $instance, $moment, $userlist = [])
    {
        global $DB, $CFG;

        if (!$moment) {
            return false;
        }
        $userrecord = $DB->get_record('user', array('id' => $userid));
        $course = $DB->get_record('course', array('id' => $instance->courseid));
        $context = \context_course::instance($instance->courseid);
        $from = get_admin();

        $a = new \stdClass();
        $a->coursename = format_string($course->fullname, true, array('context' => $context));
        $a->profileurl = "$CFG->wwwroot/user/view.php?id=$userrecord->id&course=$instance->courseid";
        $a->link = course_get_url($course)->out();
        $key = array('{COURSENAME}', '{PROFILEURL}', '{LINK}', '{FULLNAME}', '{EMAIL}');
        $value = array($a->coursename, $a->profileurl, $a->link, fullname($userrecord), $userrecord->email);

        if ($moment === 'preemail') {

            $subject = $instance->customparams->preemailsubject;
            $message = $instance->customparams->preemailbody;
        } elseif ($moment === 'postemail') {
            $subject = $instance->customparams->postemailsubject;
            $message = $instance->customparams->postemailbody;
        } else {

            return false;
        }

        if (isset($message->text)) {
            $message = $message->text;
        }

        $subject = str_replace($key, $value, $subject);
        $message = str_replace($key, $value, $message);

        if (strpos($message, '<') === false) {
            // Plain text only.
            $messagetext = $message;
            $messagehtml = text_to_html($messagetext, null, false, true);
        } else {
            // This is most probably the tag/newline soup known as FORMAT_MOODLE.
            $messagehtml = format_text($message, FORMAT_MOODLE, array(
                'context' => $context,
                'para' => false,
                'newlines' => true,
                'filter' => true
            ));
            $messagetext = html_to_text($messagehtml);
        }

        $context = \context_course::instance($instance->courseid);

        // Push notification to user in Moodle and email
        if ($userlist) {

            foreach ($userlist as $uid) {

                $this->send_moodle_notification($moment, $uid, $a->link, $a->coursename, $subject, $messagehtml, get_string('pluginname', 'local_recertification'));
                if ($moment === 'preemail') {
                    $event = \local_recertification\event\recertification_preemail_send::create(
                        array(
                            'objectid'      => $instance->courseid,
                            'relateduserid' => $uid,
                            'courseid' => $instance->courseid,
                            'context' => $context,
                        )
                    );
                } else {
                    $event = \local_recertification\event\recertification_postemail_send::create(
                        array(
                            'objectid'      => $instance->courseid,
                            'relateduserid' => $uid,
                            'courseid' => $instance->courseid,
                            'context' => $context,
                        )
                    );
                }

                $event->trigger();
            }
        } else {

            $this->send_moodle_notification($moment, $userrecord, $a->link, $a->coursename, $subject, $messagehtml, get_string('pluginname', 'local_recertification'));
            if ($moment === 'preemail') {
                $event = \local_recertification\event\recertification_preemail_send::create(
                    array(
                        'objectid'      => $instance->courseid,
                        'relateduserid' => $userid,
                        'courseid' => $instance->courseid,
                        'context' => $context,
                    )
                );
            } else {
                $event = \local_recertification\event\recertification_postemail_send::create(
                    array(
                        'objectid'      => $instance->courseid,
                        'relateduserid' => $userid,
                        'courseid' => $instance->courseid,
                        'context' => $context,
                    )
                );
            }

            $event->trigger();
        }

        return true;
    }

    private function send_moodle_notification(
        $messageprovider,
        $userid,
        $courseurl,
        $coursename,
        $subject,
        $body,
        $smallmessage
    ) {
        $message = new \core\message\message();
        $message->component = 'local_recertification'; // Your plugin's name
        $message->name = $messageprovider; // Your notification name from message.php
        $message->userfrom = \core_user::get_noreply_user(); // If the message is 'from' a specific user you can set them here
        $message->userto = $userid;
        $message->subject = $subject;
        $message->fullmessage = $body;
        $message->fullmessageformat = FORMAT_HTML;
        $message->fullmessagehtml = $body;
        $message->smallmessage = $smallmessage;
        $message->notification = 1; // Because this is a notification generated from Moodle, not a user-to-user message
        $message->contexturl = $courseurl;
        $message->contexturlname = $coursename; // Link title explaining where users get to for the contexturl

        // Actually send the message
        return message_send($message);
    }

    private function remove_completed_recertification_from_queue()
    {
        global $DB;

        $completeds = $DB->get_records_select(
            'local_recertification_queue',
            "recertificate = 1 AND sendpreemail IN (0,2) AND sendpostemail IN (0,2)"
        );

        foreach ($completeds as $completed) {

            $obj = new \stdClass;

            $obj->instanceid = $completed->instanceid;
            $obj->ueid = $completed->ueid;
            $obj->courseid = $completed->courseid;
            $obj->userid = $completed->userid;
            $obj->preemail_complete = $completed->preemail_complete;
            $obj->recertification_complete = $completed->recertification_complete;
            $obj->postemail_complete = $completed->postemail_complete;
            $obj->timecreated = time();

            $DB->insert_record('local_recertification_hist', $obj);

            $DB->delete_records('local_recertification_queue', ['id' => $completed->id]);
        }
    }
}
