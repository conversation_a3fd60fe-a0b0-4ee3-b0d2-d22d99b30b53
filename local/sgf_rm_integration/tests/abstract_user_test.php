<?php namespace local_ssystem;

use \advanced_testcase;
use local_sgf_rm_integration\models\abstract_user;

class abstract_user_test extends advanced_testcase {

    public function test_hash_api_data_is_deterministic() {
        $data = ['NOME' => '<PERSON>', 'CPF' => '12345678900'];
        $hash1 = abstract_user::hash_api_data($data);
        $hash2 = abstract_user::hash_api_data($data);
        $this->assertEquals($hash1, $hash2);
    }

    public function test_hash_api_data_changes_with_data() {
        $data1 = ['NOME' => 'Rodrigo', 'CPF' => '123'];
        $data2 = ['NOME' => 'Rodrigo', 'CPF' => '321'];
        $hash1 = abstract_user::hash_api_data($data1);
        $hash2 = abstract_user::hash_api_data($data2);
        $this->assertNotEquals($hash1, $hash2);
    }

    public function test_save_creates_user_when_not_exists(): void {
        global $DB;
        $this->resetAfterTest();
    
        $data = [
            'NOME' => '<PERSON>',
            'CPF' => '12345678901',
            'EMAIL' => '<EMAIL>',
            'SITUACAOUSUARIO' => 'ATIVO',
        ];
    
        $user = abstract_user::from_api($data);
        $expectedhash = abstract_user::hash_api_data($data);
        $user->save();
    
        $this->assertNotNull($user->id);
    
        $dbuser = $DB->get_record('user', ['id' => $user->id], 'id, username, ' . abstract_user::HASH_FIELD);
        $this->assertEquals('12345678901', $dbuser->username);
        $this->assertEquals($expectedhash, $dbuser->{abstract_user::HASH_FIELD});
    }

    public function test_save_does_not_update_when_hash_is_unchanged(): void {
        $this->resetAfterTest();
    
        $data = [
            'NOME' => 'Maria Clara',
            'CPF' => '98765432100',
            'EMAIL' => '<EMAIL>',
            'SITUACAOUSUARIO' => 'ATIVO',
        ];
    
        // Creating user
        $user = abstract_user::from_api($data)->save();
        $id_before = $user->id;
    
        // Saving same data
        $user2 = abstract_user::from_api($data);
        $user2->save();
    
        $this->assertEquals($id_before, $user2->id);
        $this->assertEquals($user->{abstract_user::HASH_FIELD}, $user2->{abstract_user::HASH_FIELD});
    }

    public function test_save_updates_when_hash_changes(): void {
        global $DB;
        $this->resetAfterTest();
    
        $data = [
            'NOME' => 'Carlos Eduardo',
            'CPF' => '11122233344',
            'EMAIL' => '<EMAIL>',
            'SITUACAOUSUARIO' => 'ATIVO',
        ];
    
        $user = abstract_user::from_api($data)->save();
        $id = $user->id;
        $oldhash = $user->{abstract_user::HASH_FIELD};
    
        // Changing something
        $data['NOME'] = 'Carlos Eduardo Silva';
        $user2 = abstract_user::from_api($data);
        $newhash = $user->{abstract_user::HASH_FIELD};
        $user2->save();
    
        $this->assertEquals($id, $user2->id);
        $this->assertNotEquals($oldhash, $user2->{abstract_user::HASH_FIELD});
    
        // Check for update
        $updated = $DB->get_record('user', ['id' => $id]);
        $this->assertEquals('Carlos', $updated->firstname);
        $this->assertEquals('Eduardo Silva', $updated->lastname);
        $this->assertEquals($user2->{abstract_user::HASH_FIELD}, $updated->{abstract_user::HASH_FIELD});
    }
}