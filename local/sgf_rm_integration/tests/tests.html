<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8"/>
        <title>Test Documentation</title>
        <style>
            body {
                text-rendering: optimizeLegibility;
                font-variant-ligatures: common-ligatures;
                font-kerning: normal;
                margin-left: 2em;
                background-color: #ffffff;
                color: #000000;
            }

            body > ul > li {
                font-family: Source Serif Pro, PT Sans, Trebuchet MS, Helvetica, Arial;
                font-size: 2em;
            }

            h2 {
                font-family: Tahoma, Helvetica, Arial;
                font-size: 3em;
            }

            ul {
                list-style: none;
                margin-bottom: 1em;
            }
        </style>
    </head>
    <body>
        <h2 id="local_ssystem\abstract_user_test">abstract_user_test (local_ssystem\abstract_user_test)</h2>
        <ul>
            <li style="color: #555753;">✓ Hash api data is deterministic</li>
            <li style="color: #555753;">✓ Hash api data changes with data</li>
            <li style="color: #555753;">✓ Save creates user when not exists</li>
            <li style="color: #555753;">✓ Save does not update when hash is unchanged</li>
            <li style="color: #555753;">✓ Save updates when hash changes</li>
        </ul>
        <h2 id="local_ssystem\import_users_from_csv_test">import_users_from_csv_test (local_ssystem\import_users_from_csv_test)</h2>
        <ul>
            <li style="color: #555753;">✓ Import users from csv</li>
        </ul>
        <h2 id="local_sgf_rm_integration\user_processor_test">user_processor_test (local_sgf_rm_integration\user_processor_test)</h2>
        <ul>
            <li style="color: #ef2929;">❌ User processor full load</li>
        </ul>
        <h2 id="local_sgf_rm_integration\user_test">user_test (local_sgf_rm_integration\user_test)</h2>
        <ul>
            <li style="color: #555753;">✓ User creation</li>
            <li style="color: #555753;">✓ User update</li>
        </ul>
    </body>
</html>