<?php namespace local_sgf_rm_integration\models;

use \local_sgf_rm_integration\models\abstract_user;
use \local_ssystem\constants\custom_profile_fields;

class rm_user extends abstract_user {
    
    const ORIGIN = 'RM';

    public static function from_api(array $data) : static {
        $instance = parent::from_api($data);

        if(empty($instance->get_profile_field(custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD))){
            $instance->set_profile_field(custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD, custom_profile_fields::OCCUPATIONAL_PROFILE_WORKER);
        }

        return $instance;
    }
}
