<?php

namespace local_courseblockapi\traits;

use context_course;
use local_gamification\repositories\reward_repository;
use local_gamification\repositories\setting_repository;
use local_ssystem\constants\custom_course_fields;
use moodle_url;
use stdClass;

require_once($CFG->dirroot . '/local/courseblockapi/locallib.php');
require_once($CFG->dirroot . '/local/courseconsentterm/lib.php');

trait course_trait
{
    /**
     * List of about course fields
     *
     * @var array
     */
    protected static $aboutcourse_fields = [
        custom_course_fields::INTRODUCTION,
        custom_course_fields::REQUIREMENTS,
        custom_course_fields::AREA,
        custom_course_fields::COMPLEXITY_LEVEL,
        custom_course_fields::TECHNICAL_SHEET,
        custom_course_fields::EVALUATION_CRITERIA,
        custom_course_fields::OBJECTIVES,
        custom_course_fields::CURRICULUM,
        custom_course_fields::TARGET_AUDIENCE,
        custom_course_fields::CONSENT_TERM,
    ];

    /**
     * Get the course image URL for a Moodle course.
     *
     * @param object $course The course object.
     * @return string The course image URL.
     */
    protected static function get_course_image(object $course, string $format = '')
    {
        global $OUTPUT, $CFG;

        $context = \context_course::instance($course->id, IGNORE_MISSING);
        $courselogo = "";
        $courseimage = "";
        $coursecard = "";
        $backupimg = "";
        $course_in_list = !is_a($course, "core_course_list_element") ? new \core_course_list_element($course) : $course;

        foreach ($course_in_list->get_course_overviewfiles() as $file) {
            if ($file->is_valid_image()) {
                $imagepath = '/' . $file->get_contextid() . '/' . $file->get_component() . '/' . $file->get_filearea() . $file->get_filepath() . $file->get_filename();

                $timemodified = $file->get_timemodified();

                $imageurl = file_encode_url($CFG->wwwroot . '/pluginfile.php', $imagepath, false);
                $imageurl .= "?rev=" . $timemodified;

                if (stristr(strtolower($file->get_filename()), "logo.")) {
                    $courselogo = $imageurl;
                } elseif (stristr(strtolower($file->get_filename()), "bg.")) {
                    $courseimage = $imageurl;
                } elseif (stristr(strtolower($file->get_filename()), "card.")) {
                    $coursecard = $imageurl;
                } else {
                    $backupimg = $imageurl;
                }
            }
        }

        if (!$courseimage) {
            $courseimage = $backupimg ?: $OUTPUT->get_generated_image_for_id($context->id);
        }

        $return = [
            'logo' => $courselogo,
            'card' => $coursecard,
            'image' => $courseimage
        ];

        return $format == 'object' ? (object) $return : $return;
    }

    /**
     * Return the gamification details for a course.
     *
     * @param int $courseid The course id.
     * @return array|null An array with points and coins or null if no reward is found.
     */
    protected static function get_course_gamification($courseid)
    {
        $config = (new setting_repository)->get_instance();
        $reward = (new reward_repository)->get_course_reward($courseid);

        if ($config?->get('enable') && $reward) {
            return [
                'points' => $reward->get_points_named(),
                'coins' => $reward->get('coins')
            ];
        }

        return [];
    }

    /**
     * Get the course URL for a Moodle course.
     *
     * @param int $courseid The ID of course.
     * @return string The course URL.
     */
    public function get_course_url($courseid)
    {
        return (new moodle_url('/course/view.php', ['id' => $courseid]))->out(false);
    }

    /**
     * Get the course custom fields.
     *
     * @param int $courseid The ID of course.
     * @return stdClass The course custom fields.
     */
    public function get_course_custom_fields($courseid)
    {
        global $DB;

        $sql = "SELECT *
                FROM {local_custom_fields_course} mcfc  
                WHERE mcfc.courseid = :courseid";

        $params = ['courseid' => $courseid];

        return $DB->get_record_sql($sql, $params);
    }

    /**
     * Get the course custom fields.
     *
     * @param int $courseid The ID of course.
     * @return 
     */
    protected static function get_total_sections($courseid)
    {
        global $DB;

        $sql = "SELECT MAX(section) num_sections
                FROM {course_sections} mcs 
                WHERE mcs.course = :courseid
                GROUP BY mcs.course";

        $params = ['courseid' => $courseid];

        return $DB->get_field_sql($sql, $params);
    }

    /**
     * Checks if the user is enrolled in the course.
     *
     * @param int $courseid The ID of course.
     * @param int $userid The ID of User.
     * @return 
     */
    public function get_course_user_is_enrolled($courseid, $userid)
    {
        global $DB;

        $sql = "SELECT 1
        FROM {user_enrolments} mue 
            JOIN {enrol} me2 ON (me2.id = mue.enrolid)
            JOIN {role_assignments} mra ON mra.userid = mue.userid AND mra.roleid = 5
            JOIN {context} mc2 ON mc2.id = mra.contextid AND mc2.instanceid = me2.courseid
        WHERE mue.status = 0 
            AND mc2.contextlevel = 50
            AND UNIX_TIMESTAMP() BETWEEN mue.timestart AND IF(NOT mue.timeend, UNIX_TIMESTAMP(), mue.timeend)
            AND me2.courseid = :courseid AND mue.userid = :userid";

        $params = [
            'courseid' => $courseid,
            'userid' => $userid
        ];

        return $DB->get_field_sql($sql, $params);
    }

    /**
     * Check if course is favorite
     *
     * @param int $courseid The ID of course.
     * @param int $userid The ID of User.
     * @return 
     */
    public function get_course_is_favourite($courseid, $userid)
    {
        return local_courseblockapi_is_course_favourite($courseid, 'core_course', $userid);
    }

    /**
     * Hidden course user preference name.
     *
     * @param string $component The component name.
     * @param int $courseid The ID of Course.
     * @return array Array of tags display names.
     */
    protected static function make_hidden_course_user_preference_name(string $component, int $courseid): string
    {
        return "{$component}_hidden_course_{$courseid}";
    }

    /**
     * If the course is hidden.
     *
     * @param string $component The component name.
     * @param int $courseid The ID of Course.
     * @param int $userid The ID of User.
     * @return array Array of tags display names.
     */
    protected static function course_is_hidden(string $component, int $courseid, int $userid): bool
    {
        return !!get_user_preferences(self::make_hidden_course_user_preference_name($component, $courseid), false, $userid);
    }

    /**
     * Toggle course visibility.
     *
     * @param string $component The component name.
     * @param int $courseid The ID of Course.
     * @param int $userid The ID of User.
     * @return bool 
     */
    protected static function toggle_course_visibility(string $component, int $courseid, int $userid): bool
    {
        $value = static::course_is_hidden($component, $courseid, $userid) ? null : true;

        return set_user_preference(self::make_hidden_course_user_preference_name($component, $courseid), $value, $userid);
    }

    /**
     * Returns user tag list.
     *
     * @param int $courseid The ID of Course.
     * @param bool $separate
     * @return array|string Array or string of tags display names.
     */
    protected static function get_course_teachers(int $courseid, $separate = false)
    {
        global $DB;

        $sql = "SELECT mu.id, CONCAT(mu.firstname, ' ', mu.lastname) teacher
            FROM {user} mu 
                JOIN {user_enrolments} mue ON mue.userid = mu.id
                JOIN {enrol} me ON me.id = mue.enrolid 
                JOIN {course} mc ON mc.id = me.courseid
                JOIN {context} mc2 ON mc2.instanceid = mc.id
                JOIN {role_assignments} mra ON mra.contextid = mc2.id AND mra.userid = mu.id
            WHERE mue.status = 0 
                AND me.status = 0 
                AND mc.visible = 1 
                AND mc2.contextlevel = 50 
                AND mra.roleid = 3 
                AND mc.id = :courseid
            GROUP BY mu.id";

        $params = ['courseid' => $courseid];

        $teachers = [];
        $strTeachers = "";

        $recordset = $DB->get_recordset_sql($sql, $params);

        foreach ($recordset as $record) {
            $teachers[] = [
                'id'   => $record->id,
                'name' => $record->teacher,
            ];
        }

        $strTeachers = self::make_teacher_string($teachers);

        if (!$separate) {
            return $teachers;
        }

        if (!$teachers) {
            return "";
        }

        return $strTeachers;
    }

    protected static function make_teacher_string(array $teachers)
    {
        $strTeachers = implode(
            ' ' . get_string('and', 'local_courseblockapi') . ' ',
            array_column($teachers, 'name')
        );

        return $strTeachers;
    }

    /**
     * Get label for number of sections and the name of the section.
     *
     * @param int $courseid The course object.
     * @param int $total_sections The count sections.
     * @return string
     */
    protected static function get_course_total_sections(int $courseid, int $total_sections = null)
    {
        $courseformat = course_get_format($courseid);

        if (is_null($total_sections)) {
            $total_sections = self::get_total_sections($courseid);
        }

        if (method_exists($courseformat, 'get_custom_section_name')) {
            if ($total_sections > 1) {
                $sectionanme = mb_strtolower($courseformat->get_custom_section_name(true));
            } else {
                $sectionanme = mb_strtolower($courseformat->get_custom_section_name());
            }
        } else {
            if (get_string_manager()->string_exists('sectionname', 'format_' . $courseformat->get_format())) {
                if ($total_sections > 1) {
                    $sectionanme = get_string("sectionname", "format_{$courseformat->get_format()}") . 's';
                } else {
                    $sectionanme = get_string("sectionname", "format_{$courseformat->get_format()}");
                }
            } else {
                if ($total_sections > 1) {
                    $sectionanme = get_string("sectionname", "core") . 's';
                } else {
                    $sectionanme = get_string("sectionname", "core");
                }
            }
        }

        return $total_sections . ' ' . mb_strtolower($sectionanme);
    }

    /**
     * Gets the custom fields data for a course.
     *
     * @param int $courseid The course id.
     * @return stdClass The custom fields data.
     */
    protected static function get_customfields_data($courseid): stdClass
    {
        global $DB;

        return $DB->get_record("local_custom_fields_course", ["courseid" => $courseid]);
    }

    /**
     * Get the about course data for a course.
     *
     * @param int $courseid The course id.
     * @return array The about course data.
     */
    protected static function get_aboutcourse_data($courseid): array
    {
        $aboutcourse_data = [];

        $customfields = self::get_customfields_data($courseid);
        $customfields_name = self::get_customfields_name();

        $index = 0;

        foreach ($customfields as $key => $value) {

            if (empty($value) || !strip_tags($value)) {
                continue;
            }

            if (in_array($key, self::$aboutcourse_fields)) {
                $data = [
                    'index'     => $index++,
                    'shortname' => $key,
                    'name'      => $customfields_name[$key],
                    'data'      => $value,
                ];

                if ($key === custom_course_fields::CONSENT_TERM && !has_capability('local/courseconsentterm:can_ignore_term', context_course::instance($courseid))) {

                    $accepted = !local_courseconsentterm_has_unaccepted_term($courseid);

                    $data['term'] = [
                        'accepted' => $accepted,
                        'tag' => local_courseconsentterm_get_accepted_tag($courseid, true),
                    ];
                }

                $aboutcourse_data[] = $data;
            }
        }

        return $aboutcourse_data;
    }

    /**
     * Returns an array of custom field names indexed by the shortname.
     *
     * @return array An array of custom field names indexed by the shortname.
     */
    protected static function get_customfields_name()
    {
        global $DB;
        $sql = "SELECT mcf.shortname, mcf.name
                FROM {customfield_field} mcf
                    JOIN {customfield_category} mcc ON mcf.categoryid = mcc.id
                WHERE mcc.component = 'core_course'
                    AND mcc.area = 'course'";

        return $DB->get_records_sql_menu($sql);
    }
}
