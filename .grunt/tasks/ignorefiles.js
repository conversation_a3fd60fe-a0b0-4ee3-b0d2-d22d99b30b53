// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
/* jshint node: true, browser: false */
/* eslint-env node */

/**
 * @copyright  2021 Andrew <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

module.exports = grunt => {

    /**
     * Generate the PHPCS configuration.
     *
     * @param {Object} thirdPartyPaths
     */
    const phpcsIgnore = (thirdPartyPaths) => {
        const {toXML} = require('jstoxml');

        const config = {
            _name: 'ruleset',
            _attrs: {
                name: "MoodleCore",
            },
            _content: [
                {
                    rule: {
                        _attrs: {
                            ref: './phpcs.xml.dist',
                        },
                    },
                },
            ],
        };

        thirdPartyPaths.forEach(library => {
            config._content.push({
                'exclude-pattern': library,
            });
        });

        grunt.file.write('phpcs.xml', toXML(config, {
            header: true,
            indent: '  ',
        }) + "\n");
    };

    /**
     * Generate ignore files (utilising thirdpartylibs.xml data)
     */
    const handler = function() {
        const path = require('path');
        const ComponentList = require(path.join(process.cwd(), '.grunt', 'components.js'));

        // An array of paths to third party directories.
        const thirdPartyPaths = ComponentList.getThirdPartyPaths();

        // Generate .eslintignore.
        const eslintIgnores = [
            '# Generated by "grunt ignorefiles"',
            // Do not ignore the .grunt directory.
            '!/.grunt',

            // Ignore all yui/src meta directories and build directories.
            '*/**/yui/src/*/meta/',
            '*/**/build/',
        ].concat(thirdPartyPaths);
        grunt.file.write('.eslintignore', eslintIgnores.join('\n') + '\n');

        // Generate .stylelintignore.
        const stylelintIgnores = [
            '# Generated by "grunt ignorefiles"',
            '**/yui/build/*',
            'theme/boost/style/moodle.css',
            'theme/classic/style/moodle.css',
            'jsdoc/styles/*.css',
            'admin/tool/componentlibrary/hugo/dist/css/docs.css',
        ].concat(thirdPartyPaths);
        grunt.file.write('.stylelintignore', stylelintIgnores.join('\n') + '\n');

        phpcsIgnore(thirdPartyPaths);
    };

    grunt.registerTask('ignorefiles', 'Generate ignore files for linters', handler);

    return handler;
};
