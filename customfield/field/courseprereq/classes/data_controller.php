<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace customfield_courseprereq;

/**
 * Manages field data
 *
 * @package    customfield_courseprereq
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class data_controller extends \core_customfield\data_controller {
    public function datafield() : string {
        return 'value';
    }

    public function instance_form_save($value) {
        $fieldname = $this->get_form_element_name();
        $value = isset($value->{$fieldname}) ? $value->{$fieldname} : "";
        
        if (is_array($value)) {
            $value = implode(',', $value);
        }

        $this->data->set('value', $value);
        //$this->data->set('valueformat', FORMAT_MOODLE);
        $this->save();
    }
    
    public function instance_form_before_set_data($instance) {
        $value = $this->get('value');
        $fieldname = $this->get_form_element_name();
        
        if (!empty($value)) {
            $instance->{$fieldname} = explode(',', $value);
        } else {
            $instance->{$fieldname} = [];
        }
    }

    public function instance_form_definition(\MoodleQuickForm $mform) {
        $field = $this->get_field();
        $field->field_definition($mform);        
    }

    public function instance_form_validation(array $data, array $files) : array {
        $errors = parent::instance_form_validation($data, $files);
        return $errors;
    }

    public function get_default_value() {
        return $this->get_field()->get_configdata_property('defaultvalue');
    }

    public function export_value() {
        return $this->get('value');
    }   
}