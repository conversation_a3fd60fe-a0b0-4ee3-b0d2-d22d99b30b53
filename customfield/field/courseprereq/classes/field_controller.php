<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace customfield_courseprereq;

/**
 * Manages field information (without data)
 *
 * @package    customfield_courseprereq
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class field_controller extends \core_customfield\field_controller
{
    /**
     * Defines the configuration form elements for the field.
     *
     * @param \MoodleQuickForm $mform The Moodle form object
     */
    public function config_form_definition(\MoodleQuickForm $mform) {}

    /**
     * Defines the field form elements.
     *
     * Adds an autocomplete element to the form with multiple selection enabled.
     *
     * @param \MoodleQuickForm $mform The Moodle form object
     */
    public function field_definition(\MoodleQuickForm $mform)
    {
        $configdata = $this->get('configdata');

        $options = [];

        $fieldname = "customfield_" . $this->get('shortname');

        $mform->addElement(
            'autocomplete',
            $fieldname,
            $this->get_formatted_name(),
            $options,
            ['multiple' => true]
        );
        // $mform->setType($fieldname, PARAM_INT);
        // $mform->setDefault($fieldname, $default);
    }

    /**
     * Validates the configuration form data.
     *
     * @param array $data The submitted form data
     * @param array $files The uploaded files (if any)
     * @return array An array of errors, if any
     */
    public function config_form_validation(array $data, $files = array()): array
    {
        $errors = parent::config_form_validation($data, $files);
        $configdata = $data['configdata'];

        return $errors;
    }
}
