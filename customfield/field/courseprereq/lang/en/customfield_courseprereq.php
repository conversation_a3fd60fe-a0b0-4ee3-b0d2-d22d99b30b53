<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Multiple Course Prerequisites Selector
 *
 * @package    customfield_courseprereq
 * @category   string
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Multiple Course Prerequisites Selector';
$string['privacy:metadata'] = 'The Multiple Course Prerequisites Selector field type plugin doesn\'t store any personal data; it uses tables defined in core.';
$string['specificsettings'] = 'Multiple Course Prerequisites Selector field settings';
$string['fieldname'] = 'Course Prerequisites';
$string['fielddescription'] = 'Select multiple courses as prerequisites';
$string['filterhidden'] = 'Show hidden courses';
$string['filterfuture'] = 'Show future courses';
$string['filterexpired'] = 'Show expired courses';
